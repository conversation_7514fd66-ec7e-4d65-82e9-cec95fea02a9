add_library(Main
  STATIC
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppAllocator.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppAllocator.hpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppDefine.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppDefine.hpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppPal.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppPal.hpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppTextureManager.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppTextureManager.hpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppModel.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/LAppModel.hpp
  ${CMAKE_CURRENT_SOURCE_DIR}/Log.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/Log.hpp
  ${CMAKE_CURRENT_SOURCE_DIR}/MatrixManager.cpp
  ${CMAKE_CURRENT_SOURCE_DIR}/MatrixManager.hpp
  ${CMAKE_CURRENT_SOURCE_DIR}/HackProperties.h
)

