{"version": "Sample 1.0.0", "model": "haru.moc", "textures": ["haru.1024/texture_00.png", "haru.1024/texture_01.png", "haru.1024/texture_02.png"], "motions": {"idle": [{"file": "motions/haru_idle_01.mtn"}, {"file": "motions/haru_idle_02.mtn"}, {"file": "motions/haru_idle_03.mtn"}], "null": [{"file": "motions/haru_m_01.mtn"}, {"file": "motions/haru_m_02.mtn", "fade_in": 500}, {"file": "motions/haru_m_03.mtn"}, {"file": "motions/haru_m_04.mtn"}, {"file": "motions/haru_m_05.mtn"}, {"file": "motions/haru_m_06.mtn"}, {"file": "motions/haru_m_07.mtn", "fade_in": 300, "fade_out": 500}, {"file": "motions/haru_m_08.mtn"}, {"file": "motions/haru_m_09.mtn"}, {"file": "motions/haru_m_10.mtn"}, {"file": "motions/haru_normal_01.mtn", "sound": "sounds/haru_normal_01.mp3"}, {"file": "motions/haru_normal_02.mtn", "sound": "sounds/haru_normal_02.mp3"}, {"file": "motions/haru_normal_03.mtn", "fade_in": 500, "sound": "sounds/haru_normal_03.mp3"}, {"file": "motions/haru_normal_04.mtn", "sound": "sounds/haru_normal_04.mp3"}, {"file": "motions/haru_normal_05.mtn", "sound": "sounds/haru_normal_05.mp3"}, {"file": "motions/haru_normal_06.mtn", "sound": "sounds/haru_normal_06.mp3"}, {"file": "motions/haru_normal_07.mtn", "fade_in": 700, "sound": "sounds/haru_normal_07.mp3"}, {"file": "motions/haru_normal_08.mtn", "fade_in": 500, "sound": "sounds/haru_normal_08.mp3"}, {"file": "motions/haru_normal_09.mtn", "sound": "sounds/haru_normal_09.mp3"}, {"file": "motions/haru_normal_10.mtn", "sound": "sounds/haru_normal_10.mp3"}]}, "expressions": [{"name": "f01.exp.json", "file": "expressions/f01.exp.json"}, {"name": "f02.exp.json", "file": "expressions/f02.exp.json"}, {"name": "f03.exp.json", "file": "expressions/f03.exp.json"}, {"name": "f04.exp.json", "file": "expressions/f04.exp.json"}, {"name": "f05.exp.json", "file": "expressions/f05.exp.json"}, {"name": "f06.exp.json", "file": "expressions/f06.exp.json"}, {"name": "f07.exp.json", "file": "expressions/f07.exp.json"}, {"name": "f08.exp.json", "file": "expressions/f08.exp.json"}], "physics": "haru.physics.json", "pose": "haru.pose.json"}