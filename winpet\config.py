#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
管理桌面伴侣的各种设置和配置
"""

import os
import json
from pathlib import Path


class Config:
    """配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        # 配置文件路径
        self.config_dir = Path.home() / ".winpet"
        self.config_file = self.config_dir / "config.json"
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "window": {
                "always_on_top": True,
                "size": [200, 200],
                "position": [100, 100],
                "opacity": 1.0
            },
            "features": {
                "screen_monitor": True,
                "auto_hide": False,
                "auto_start": False
            },
            "display": {
                "model_type": "placeholder",  # placeholder, live2d, image, gif
                "model_path": "",
                "animation_speed": 1.0
            },
            "system": {
                "language": "zh_CN",
                "theme": "default"
            }
        }
        
        # 加载配置
        self.config = self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有必要的键都存在
                    return self._merge_config(self.default_config, config)
            else:
                # 配置文件不存在，使用默认配置并保存
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
            
    def save_config(self, config=None):
        """保存配置文件"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
            
    def _merge_config(self, default, loaded):
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
        
    def get(self, key_path, default=None):
        """
        获取配置值
        :param key_path: 配置键路径，如 "window.size"
        :param default: 默认值
        :return: 配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key_path, value):
        """
        设置配置值
        :param key_path: 配置键路径，如 "window.size"
        :param value: 配置值
        """
        keys = key_path.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
            
        # 设置值
        config[keys[-1]] = value
        
        # 保存配置
        self.save_config()
        
    def get_window_config(self):
        """获取窗口配置"""
        return self.config.get("window", {})
        
    def get_features_config(self):
        """获取功能配置"""
        return self.config.get("features", {})
        
    def get_display_config(self):
        """获取显示配置"""
        return self.config.get("display", {})
        
    def get_system_config(self):
        """获取系统配置"""
        return self.config.get("system", {})
        
    def update_window_position(self, x, y):
        """更新窗口位置"""
        self.set("window.position", [x, y])
        
    def update_window_size(self, width, height):
        """更新窗口大小"""
        self.set("window.size", [width, height])
        
    def toggle_always_on_top(self):
        """切换置顶显示"""
        current = self.get("window.always_on_top", True)
        self.set("window.always_on_top", not current)
        return not current
        
    def toggle_screen_monitor(self):
        """切换屏幕监控"""
        current = self.get("features.screen_monitor", True)
        self.set("features.screen_monitor", not current)
        return not current
        
    def toggle_auto_hide(self):
        """切换自动隐藏"""
        current = self.get("features.auto_hide", False)
        self.set("features.auto_hide", not current)
        return not current
        
    def toggle_auto_start(self):
        """切换开机启动"""
        current = self.get("features.auto_start", False)
        self.set("features.auto_start", not current)
        return not current


# 全局配置实例
config = Config()
