{"Version": 3, "Meta": {"Duration": 3.667, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 40, "TotalSegmentCount": 160, "TotalPointCount": 426, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param77", "Segments": [0, 1, 0, 3.667, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, -30, 0.167, -30, 1, 0.278, -30, 0.389, 30, 0.5, 30, 1, 1.444, 30, 2.389, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, -30, 0.167, -30, 1, 0.278, -30, 0.389, -30, 0.5, -30, 1, 0.667, -30, 0.833, 30, 1, 30, 1, 1.078, 30, 1.156, -30, 1.233, -30, 1, 1.378, -30, 1.522, 30, 1.667, 30, 1, 1.744, 30, 1.822, -30, 1.9, -30, 1, 2.044, -30, 2.189, 30, 2.333, 30, 1, 2.4, 30, 2.467, -30, 2.533, -30, 1, 2.689, -30, 2.844, 30, 3, 30, 1, 3.056, 30, 3.111, -30, 3.167, -30, 1, 3.222, -30, 3.278, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 1, 1, 0.056, 1, 0.111, -30, 0.167, -30, 1, 0.278, -30, 0.389, -30, 0.5, -30, 1, 0.611, -30, 0.722, -30, 0.833, -30, 1, 1.667, -30, 2.5, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, -10, 0.167, -10, 1, 0.278, -10, 0.389, -4.359, 0.5, 2, 1, 0.611, 8.359, 0.722, 10, 0.833, 10, 1, 1.667, 10, 2.5, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, -10, 0.167, -10, 1, 0.278, -10, 0.389, 10, 0.5, 10, 1, 0.611, 10, 0.722, 10, 0.833, 10, 1, 1.667, 10, 2.5, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.611, 0, 1.222, 1, 1.833, 1, 1, 2.444, 1, 3.056, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.5, 0, 1, 9, 1.5, 9, 1, 1.511, 9, 1.522, 0, 1.533, 0, 1, 1.967, 0, 2.4, 9, 2.833, 9, 1, 3, 9, 3.167, 5.7, 3.333, 5.7, 0, 3.667, 5.7]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.878, 0, 1.189, 1, 1.5, 1, 1, 1.811, 1, 2.122, 1, 2.433, 1, 1, 2.644, 1, 2.856, 0, 3.067, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 0.778, 0, 1.222, 0, 1.667, 0, 1, 2.111, 0, 2.556, 0, 3, 0, 1, 3.111, 0, 3.222, 1, 3.333, 1, 0, 3.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 0.778, 0, 1.222, 0, 1.667, 0, 1, 2.111, 0, 2.556, 0, 3, 0, 1, 3.111, 0, 3.222, 1, 3.333, 1, 0, 3.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0.857, 0.333, 0.9, 1, 0.456, 0.995, 0.578, 1, 0.7, 1, 1, 1.189, 1, 1.678, 1, 2.167, 1, 1, 2.556, 1, 2.944, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 0.167, 0, 1, 0.222, 0, 0.278, 1, 0.333, 1, 1, 0.456, 1, 0.578, 1, 0.7, 1, 1, 1.189, 1, 1.678, 1, 2.167, 1, 1, 2.556, 1, 2.944, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.256, 0, 0.511, 1, 0.767, 1, 1, 1.122, 1, 1.478, 1, 1.833, 1, 1, 1.956, 1, 2.078, 1, 2.2, 1, 1, 2.356, 1, 2.511, 1, 2.667, 1, 1, 2.778, 1, 2.889, 1, 3, 1, 1, 3.111, 1, 3.222, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.333, 0, 1, 0.478, 0, 0.622, 0, 0.767, 0, 1, 1.122, 0, 1.478, 0, 1.833, 0, 1, 1.956, 0, 2.078, 0.5, 2.2, 0.5, 1, 2.356, 0.5, 2.511, 0.471, 2.667, 0.3, 1, 2.778, 0.178, 2.889, 0, 3, 0, 1, 3.111, 0, 3.222, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp1", "Segments": [0, 0, 0, 0.167, 0, 1, 0.333, 0, 0.5, 0, 0.667, 0, 1, 0.722, 0, 0.778, -1, 0.833, -1, 1, 1.389, -1, 1.944, -1, 2.5, -1, 1, 2.667, -1, 2.833, 0, 3, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp2", "Segments": [0, 0, 0, 0.167, 0, 1, 0.389, 0, 0.611, 0, 0.833, 0, 1, 1.167, 0, 1.5, 0, 1.833, 0, 1, 1.944, 0, 2.056, -1, 2.167, -1, 1, 2.433, -1, 2.7, -1, 2.967, -1, 1, 2.978, -1, 2.989, 0, 3, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp3", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp4", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp5", "Segments": [0, 0, 0, 0.167, 0, 1, 0.389, 0, 0.611, 1, 0.833, 1, 1, 0.944, 1, 1.056, 0, 1.167, 0, 1, 1.778, 0, 2.389, 0, 3, 0, 1, 3.111, 0, 3.222, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp10", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp6", "Segments": [0, 0, 0, 0.167, 0, 1, 0.389, 0, 0.611, 0, 0.833, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp7", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp8", "Segments": [0, 0, 0, 0.167, 0, 1, 0.389, 0, 0.611, 0, 0.833, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp9", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "exp12", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.167, 0, 0.333, -0.4, 0.5, -0.4, 1, 0.722, -0.4, 0.944, 1, 1.167, 1, 1, 1.556, 1, 1.944, -0.3, 2.333, -0.3, 1, 2.667, -0.3, 3, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.611, 0, 0.722, 0, 0.833, 0, 1, 1.5, 0, 2.167, 0, 2.833, 0, 1, 2.889, 0, 2.944, 0.9, 3, 0.9, 1, 3.111, 0.9, 3.222, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0.1, 0.5, 0.1, 1, 0.611, 0.1, 0.722, 0, 0.833, 0, 1, 1.5, 0, 2.167, 0, 2.833, 0, 1, 2.889, 0, 2.944, 0.9, 3, 0.9, 1, 3.111, 0.9, 3.222, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 1, 0.167, 0, 0.333, 8, 0.5, 8, 1, 0.611, 8, 0.722, -7, 0.833, -7, 1, 1.556, -7, 2.278, -6.521, 3, -5, 1, 3.111, -4.766, 3.222, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 1, 0.111, 0, 0.222, -1.2, 0.333, -1.2, 1, 0.389, -1.2, 0.444, 2, 0.5, 2, 1, 0.611, 2, 0.722, 0, 0.833, 0, 1, 1.389, 0, 1.944, 0.6, 2.5, 0.6, 1, 2.611, 0.6, 2.722, 0, 2.833, 0, 1, 2.889, 0, 2.944, 0.9, 3, 0.9, 1, 3.111, 0.9, 3.222, 0, 3.333, 0, 0, 3.667, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 1, 0.111, 0, 0.222, -1.2, 0.333, -1.2, 1, 0.389, -1.2, 0.444, 2, 0.5, 2, 1, 0.611, 2, 0.722, -0.7, 0.833, -0.7, 1, 1.389, -0.7, 1.944, 0.7, 2.5, 0.7, 1, 2.611, 0.7, 2.722, 0, 2.833, 0, 1, 2.889, 0, 2.944, 1.1, 3, 1.1, 1, 3.111, 1.1, 3.222, 0, 3.333, 0, 0, 3.667, 0]}]}