#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
右键菜单系统
实现桌面伴侣的右键菜单功能，包含退出程序和功能开关选项
"""

from PySide6.QtWidgets import QMenu, QApplication
from PySide6.QtCore import Qt, QPoint
from PySide6.QtGui import QAction, QIcon, QCursor


class RightClickMenu:
    """右键菜单类"""
    
    def __init__(self, parent):
        """
        初始化右键菜单
        :param parent: 父窗口（PetCompanion实例）
        """
        self.parent = parent
        self.menu = QMenu(parent)
        self._setup_menu()
        
    def _setup_menu(self):
        """设置菜单项"""
        # 设置菜单样式
        self.menu.setStyleSheet("""
            QMenu {
                background-color: rgba(50, 50, 50, 200);
                border: 1px solid rgba(255, 255, 255, 100);
                border-radius: 8px;
                padding: 5px;
                color: white;
                font-size: 12px;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: rgba(100, 150, 200, 150);
            }
            QMenu::item:pressed {
                background-color: rgba(100, 150, 200, 200);
            }
            QMenu::separator {
                height: 1px;
                background-color: rgba(255, 255, 255, 50);
                margin: 5px 10px;
            }
        """)
        
        # 功能开关子菜单
        self.features_menu = QMenu("功能设置", self.menu)
        self.features_menu.setStyleSheet(self.menu.styleSheet())
        
        # 置顶显示开关
        self.always_on_top_action = QAction("置顶显示", self.features_menu)
        self.always_on_top_action.setCheckable(True)
        self.always_on_top_action.setChecked(True)  # 默认开启
        self.always_on_top_action.triggered.connect(self._toggle_always_on_top)
        self.features_menu.addAction(self.always_on_top_action)
        
        # 屏幕监控开关
        self.screen_monitor_action = QAction("屏幕监控", self.features_menu)
        self.screen_monitor_action.setCheckable(True)
        self.screen_monitor_action.setChecked(True)  # 默认开启
        self.screen_monitor_action.triggered.connect(self._toggle_screen_monitor)
        self.features_menu.addAction(self.screen_monitor_action)
        
        # 自动隐藏开关
        self.auto_hide_action = QAction("自动隐藏", self.features_menu)
        self.auto_hide_action.setCheckable(True)
        self.auto_hide_action.setChecked(False)  # 默认关闭
        self.auto_hide_action.triggered.connect(self._toggle_auto_hide)
        self.features_menu.addAction(self.auto_hide_action)
        
        # 添加功能设置子菜单到主菜单
        self.menu.addMenu(self.features_menu)
        
        # 分隔线
        self.menu.addSeparator()
        
        # 显示/隐藏动作
        self.toggle_visibility_action = QAction("隐藏伴侣", self.menu)
        self.toggle_visibility_action.triggered.connect(self._toggle_visibility)
        self.menu.addAction(self.toggle_visibility_action)
        
        # 分隔线
        self.menu.addSeparator()
        
        # 关于信息
        self.about_action = QAction("关于", self.menu)
        self.about_action.triggered.connect(self._show_about)
        self.menu.addAction(self.about_action)
        
        # 分隔线
        self.menu.addSeparator()
        
        # 退出程序
        self.quit_action = QAction("退出程序", self.menu)
        self.quit_action.triggered.connect(self._quit_application)
        self.menu.addAction(self.quit_action)
        
    def show_menu(self, pos):
        """
        在指定位置显示菜单
        :param pos: 显示位置（全局坐标）
        """
        # 更新菜单项状态
        self._update_menu_items()
        
        # 调整菜单位置，确保不超出屏幕边界
        adjusted_pos = self._adjust_menu_position(pos)
        
        # 显示菜单
        self.menu.popup(adjusted_pos)
        
    def _adjust_menu_position(self, pos):
        """
        调整菜单位置，确保不超出屏幕边界
        :param pos: 原始位置
        :return: 调整后的位置
        """
        screen = QApplication.primaryScreen().availableGeometry()
        menu_size = self.menu.sizeHint()
        
        x = pos.x()
        y = pos.y()
        
        # 检查右边界
        if x + menu_size.width() > screen.right():
            x = screen.right() - menu_size.width()
            
        # 检查下边界
        if y + menu_size.height() > screen.bottom():
            y = pos.y() - menu_size.height()
            
        # 检查左边界
        if x < screen.left():
            x = screen.left()
            
        # 检查上边界
        if y < screen.top():
            y = screen.top()
            
        return QPoint(x, y)
        
    def _update_menu_items(self):
        """更新菜单项状态"""
        # 更新显示/隐藏文本
        if self.parent.isVisible():
            self.toggle_visibility_action.setText("隐藏伴侣")
        else:
            self.toggle_visibility_action.setText("显示伴侣")
            
    def _toggle_always_on_top(self):
        """切换置顶显示"""
        is_on_top = self.always_on_top_action.isChecked()
        
        # 获取当前窗口标志
        flags = self.parent.windowFlags()
        
        if is_on_top:
            # 添加置顶标志
            flags |= Qt.WindowStaysOnTopHint
        else:
            # 移除置顶标志
            flags &= ~Qt.WindowStaysOnTopHint
            
        # 应用新的窗口标志
        self.parent.setWindowFlags(flags)
        self.parent.show()  # 重新显示窗口以应用标志
        
    def _toggle_screen_monitor(self):
        """切换屏幕监控功能"""
        is_enabled = self.screen_monitor_action.isChecked()
        # TODO: 实现屏幕监控功能的开启/关闭
        print(f"屏幕监控功能: {'开启' if is_enabled else '关闭'}")
        
    def _toggle_auto_hide(self):
        """切换自动隐藏功能"""
        is_enabled = self.auto_hide_action.isChecked()
        # TODO: 实现自动隐藏功能的开启/关闭
        print(f"自动隐藏功能: {'开启' if is_enabled else '关闭'}")
        
    def _toggle_visibility(self):
        """切换显示/隐藏状态"""
        self.parent.toggle_visibility()
        
    def _show_about(self):
        """显示关于信息"""
        from PySide6.QtWidgets import QMessageBox
        
        about_text = """
        <h3>桌面伴侣 v1.0.0</h3>
        <p>基于 DyberPet 架构开发的桌面伴侣应用</p>
        <p><b>功能特性：</b></p>
        <ul>
        <li>透明背景窗口</li>
        <li>鼠标拖拽移动</li>
        <li>右键菜单控制</li>
        <li>系统托盘支持</li>
        <li>屏幕监控功能</li>
        </ul>
        <p><b>开发框架：</b> PySide6</p>
        <p><b>参考项目：</b> DyberPet, live2d-py</p>
        """
        
        msg_box = QMessageBox(self.parent)
        msg_box.setWindowTitle("关于桌面伴侣")
        msg_box.setText(about_text)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: rgba(50, 50, 50, 240);
                color: white;
            }
            QMessageBox QPushButton {
                background-color: rgba(100, 150, 200, 150);
                border: 1px solid rgba(255, 255, 255, 100);
                border-radius: 4px;
                padding: 5px 15px;
                color: white;
            }
            QMessageBox QPushButton:hover {
                background-color: rgba(100, 150, 200, 200);
            }
        """)
        msg_box.exec()
        
    def _quit_application(self):
        """退出应用程序"""
        self.parent.quit_application()
