{"Version": 3, "Meta": {"Duration": 4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 52, "TotalSegmentCount": 128, "TotalPointCount": 306, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 1.5, 0, 1, 1.556, 0, 1.611, -30, 1.667, -30, 1, 1.778, -30, 1.889, -26.879, 2, -15, 1, 2.056, -9.061, 2.111, 0, 2.167, 0, 1, 2.233, 0, 2.3, -30, 2.367, -30, 1, 2.422, -30, 2.478, 30, 2.533, 30, 1, 2.589, 30, 2.644, -15, 2.7, -15, 1, 2.756, -15, 2.811, 0, 2.867, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 30, 0.5, 30, 1, 0.556, 30, 0.611, -30, 0.667, -30, 1, 0.722, -30, 0.778, -9, 0.833, -9, 1, 0.944, -9, 1.056, -30, 1.167, -30, 1, 1.611, -30, 2.056, 30, 2.5, 30, 1, 2.611, 30, 2.722, 1.384, 2.833, 0, 1, 2.933, -1.246, 3.033, -1, 3.133, -1, 0, 4, -1]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 4, 0.5, 4, 1, 0.556, 4, 0.611, -5, 0.667, -5, 1, 0.722, -5, 0.778, 0, 0.833, 0, 1, 1.222, 0, 1.611, -5, 2, -5, 1, 2.278, -5, 2.556, 0, 2.833, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0.1, 0, 0.167, 0.1, 1, 1.111, 0.1, 2.056, 1, 3, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 0.167, 0, 1, 0.611, 0, 1.056, 0.998, 1.5, 4.5, 1, 1.778, 6.689, 2.056, 9, 2.333, 9, 1, 2.556, 9, 2.778, 0, 3, 0, 1, 3.111, 0, 3.222, 0.8, 3.333, 0.8, 0, 4, 0.8]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 1, 1, 1, 1.111, 1, 1.222, 0, 1.333, 0, 1, 1.722, 0, 2.111, 0, 2.5, 0, 1, 2.611, 0, 2.722, 1, 2.833, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 1, 1, 1, 1.111, 1, 1.222, 0, 1.333, 0, 1, 1.722, 0, 2.111, 0, 2.5, 0, 1, 2.611, 0, 2.722, 1, 2.833, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 1.5, 0, 1, 1.833, 0, 2.167, 1, 2.5, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 1.5, 0, 1, 1.833, 0, 2.167, 1, 2.5, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 1.333, 0, 1, 1.444, 0, 1.556, -1, 1.667, -1, 1, 1.778, -1, 1.889, -1, 2, -1, 1, 2.056, -1, 2.111, -1, 2.167, -1, 1, 2.222, -1, 2.278, -1, 2.333, -1, 1, 2.5, -1, 2.667, 1, 2.833, 1, 1, 2.889, 1, 2.944, 0, 3, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 1.333, 0, 1, 1.444, 0, 1.556, 0, 1.667, 0, 1, 1.778, 0, 1.889, 0.1, 2, 0.5, 1, 2.056, 0.7, 2.111, 1, 2.167, 1, 1, 2.222, 1, 2.278, 1, 2.333, 1, 1, 2.5, 1, 2.667, 0.9, 2.833, 0.5, 1, 2.889, 0.367, 2.944, 0, 3, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp1", "Segments": [0, 0, 0, 0.167, 0, 1, 0.389, 0, 0.611, 0, 0.833, 0, 1, 0.944, 0, 1.056, 1, 1.167, 1, 1, 1.722, 1, 2.278, 1, 2.833, 1, 1, 2.944, 1, 3.056, 0, 3.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp2", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp11", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp3", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp4", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp5", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp10", "Segments": [0, 0, 0, 0.167, 0, 1, 0.778, 0, 1.389, 0, 2, 0, 1, 2.056, 0, 2.111, 1, 2.167, 1, 1, 2.444, 1, 2.722, 1, 3, 1, 1, 3.056, 1, 3.111, 0, 3.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp6", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp7", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp8", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp9", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "exp12", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 1, 0.333, 0, 0.667, -0.4, 1, -0.4, 1, 1.344, -0.4, 1.689, 0.4, 2.033, 0.4, 1, 2.3, 0.4, 2.567, 0, 2.833, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 0.6, 0, 1, 0.744, 0, 0.889, 0.6, 1.033, 0.6, 1, 1.133, 0.6, 1.233, -0.349, 1.333, -0.4, 1, 1.8, -0.64, 2.267, -0.7, 2.733, -0.7, 1, 2.844, -0.7, 2.956, 0, 3.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 0.6, 0, 1, 0.744, 0, 0.889, 0.6, 1.033, 0.6, 1, 1.133, 0.6, 1.233, -0.349, 1.333, -0.4, 1, 1.8, -0.64, 2.267, -0.7, 2.733, -0.7, 1, 2.844, -0.7, 2.956, 0, 3.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 0, 4, 0]}]}