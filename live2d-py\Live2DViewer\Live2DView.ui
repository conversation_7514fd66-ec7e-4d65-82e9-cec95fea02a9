<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Live2DView</class>
 <widget class="QWidget" name="Live2DView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>645</width>
    <height>419</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Live2DView</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <widget class="QSplitter" name="splitter_2">
      <property name="orientation">
       <enum>Qt::Orientation::Vertical</enum>
      </property>
      <widget class="QTreeWidget" name="treeWidget">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="rootIsDecorated">
        <bool>true</bool>
       </property>
       <property name="headerHidden">
        <bool>true</bool>
       </property>
       <column>
        <property name="text">
         <string notr="true">1</string>
        </property>
       </column>
      </widget>
      <widget class="QWidget" name="verticalLayoutWidget">
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QComboBox" name="pageSelector">
          <item>
           <property name="text">
            <string>参数</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>部件</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>图形网格</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <widget class="QStackedWidget" name="pages">
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="paramPage">
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <property name="topMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QCheckBox" name="autoPhysics">
                <property name="text">
                 <string>物理计算</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="autoBreath">
                <property name="text">
                 <string>自动呼吸</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="autoBlink">
                <property name="text">
                 <string>自动眨眼</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QTableWidget" name="paramTable">
              <attribute name="horizontalHeaderCascadingSectionResizes">
               <bool>true</bool>
              </attribute>
              <attribute name="horizontalHeaderDefaultSectionSize">
               <number>110</number>
              </attribute>
              <attribute name="horizontalHeaderHighlightSections">
               <bool>true</bool>
              </attribute>
              <attribute name="horizontalHeaderStretchLastSection">
               <bool>true</bool>
              </attribute>
              <attribute name="verticalHeaderVisible">
               <bool>false</bool>
              </attribute>
              <attribute name="verticalHeaderHighlightSections">
               <bool>true</bool>
              </attribute>
              <column>
               <property name="text">
                <string>Id</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>名称</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>控制</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>值</string>
               </property>
              </column>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="partPage">
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <item>
             <widget class="QTableWidget" name="partTable">
              <attribute name="horizontalHeaderCascadingSectionResizes">
               <bool>false</bool>
              </attribute>
              <attribute name="horizontalHeaderStretchLastSection">
               <bool>true</bool>
              </attribute>
              <attribute name="verticalHeaderVisible">
               <bool>false</bool>
              </attribute>
              <column>
               <property name="text">
                <string>Id</string>
               </property>
              </column>
              <column>
               <property name="text">
                <string>名称</string>
               </property>
              </column>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="drawablePage">
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <item>
             <widget class="QListWidget" name="drawableList"/>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="Live2DScene" name="scene">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>Live2DScene</class>
   <extends>QOpenGLWidget</extends>
   <header location="global">scene/Live2DScene.hpp</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>pageSelector</sender>
   <signal>currentIndexChanged(int)</signal>
   <receiver>pages</receiver>
   <slot>setCurrentIndex(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>226</x>
     <y>235</y>
    </hint>
    <hint type="destinationlabel">
     <x>226</x>
     <y>330</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
