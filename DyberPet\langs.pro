SOURCES += DyberPet/DyberSettings/DyberControlPanel.py \
        DyberPet/DyberSettings/BasicSettingUI.py \
        DyberPet/DyberSettings/CharCardUI.py \
        DyberPet/DyberSettings/GameSaveUI.py \
        DyberPet/DyberSettings/ItemCardUI.py \
        DyberPet/DyberSettings/custom_base.py \
        DyberPet/DyberSettings/custom_utils.py \
        DyberPet/Accessory.py \
        DyberPet/DyberPet.py \
        DyberPet/modules.py \
        DyberPet/extra_windows.py \
        DyberPet/Notification.py \
        DyberPet/Dashboard/DashboardUI.py \
        DyberPet/Dashboard/animationUI.py \
        DyberPet/Dashboard/buffModule.py \
        DyberPet/Dashboard/dashboard_widgets.py \
        DyberPet/Dashboard/inventoryUI.py \
        DyberPet/Dashboard/shopUI.py \
        DyberPet/Dashboard/statusUI.py \
        DyberPet/Dashboard/taskUI.py \
        DyberPet/Dashboard/animDesignUI.py \




TRANSLATIONS += res/language/langs.zh_CN.blank.ts