{"Version": 3, "Meta": {"Duration": 3.333, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 15, "TotalSegmentCount": 87, "TotalPointCount": 262, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, -30, 0.167, -30, 1, 0.278, -30, 0.389, 30, 0.5, 30, 1, 0.556, 30, 0.611, 30, 0.667, 30, 1, 0.722, 30, 0.778, 18, 0.833, 18, 1, 0.889, 18, 0.944, 30, 1, 30, 1, 1.056, 30, 1.111, 18, 1.167, 18, 1, 1.222, 18, 1.278, 30, 1.333, 30, 1, 1.389, 30, 1.444, 18, 1.5, 18, 1, 1.556, 18, 1.611, 30, 1.667, 30, 1, 1.722, 30, 1.778, 18, 1.833, 18, 1, 1.889, 18, 1.944, 30, 2, 30, 1, 2.056, 30, 2.111, 18, 2.167, 18, 1, 2.222, 18, 2.278, 30, 2.333, 30, 1, 2.389, 30, 2.444, 18, 2.5, 18, 1, 2.556, 18, 2.611, 30, 2.667, 30, 1, 2.722, 30, 2.778, 18, 2.833, 18, 1, 2.889, 18, 2.944, 30, 3, 30, 1, 3.111, 30, 3.222, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, -30, 0.167, -30, 1, 0.278, -30, 0.389, 30, 0.5, 30, 1, 0.556, 30, 0.611, 30, 0.667, 30, 1, 0.722, 30, 0.778, 0, 0.833, 0, 1, 0.889, 0, 0.944, 30, 1, 30, 1, 1.056, 30, 1.111, 0, 1.167, 0, 1, 1.222, 0, 1.278, 30, 1.333, 30, 1, 1.389, 30, 1.444, 0, 1.5, 0, 1, 1.556, 0, 1.611, 30, 1.667, 30, 1, 1.722, 30, 1.778, 0, 1.833, 0, 1, 1.889, 0, 1.944, 30, 2, 30, 1, 2.056, 30, 2.111, 0, 2.167, 0, 1, 2.222, 0, 2.278, 30, 2.333, 30, 1, 2.389, 30, 2.444, 0, 2.5, 0, 1, 2.556, 0, 2.611, 30, 2.667, 30, 1, 2.722, 30, 2.778, 0, 2.833, 0, 1, 2.889, 0, 2.944, 30, 3, 30, 1, 3.111, 30, 3.222, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 1, 1, 0.056, 1, 0.111, -30, 0.167, -30, 1, 0.278, -30, 0.389, -30, 0.5, -30, 1, 1.444, -30, 2.389, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, -10, 0.167, -10, 1, 0.278, -10, 0.389, 2, 0.5, 2, 1, 1.444, 2, 2.389, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, -10, 0.167, -10, 1, 0.278, -10, 0.389, 10, 0.5, 10, 1, 1.444, 10, 2.389, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 0.944, 0, 1.889, 9, 2.833, 9, 1, 3, 9, 3.167, 5.7, 3.333, 5.7]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 0.878, 0, 1.189, 1, 1.5, 1, 1, 1.811, 1, 2.122, 1, 2.433, 1, 1, 2.644, 1, 2.856, 0, 3.067, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 0, 0.167, 0, 1, 0.222, 0, 0.278, 0, 0.333, 0, 1, 0.778, 0, 1.222, 0, 1.667, 0, 1, 1.722, 0, 1.778, 0.452, 1.833, 0.5, 1, 1.944, 0.595, 2.056, 0.6, 2.167, 0.6, 1, 2.222, 0.6, 2.278, 0, 2.333, 0, 1, 2.556, 0, 2.778, 0, 3, 0, 1, 3.111, 0, 3.222, 1, 3.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.056, 1, 0.111, 0.72, 0.167, 0.4, 1, 0.222, 0.08, 0.278, 0, 0.333, 0, 1, 0.778, 0, 1.222, 0, 1.667, 0, 1, 2.111, 0, 2.556, 0, 3, 0, 1, 3.111, 0, 3.222, 1, 3.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0.9, 0, 0.333, 0.9, 1, 0.833, 0.9, 1.333, 1, 1.833, 1, 1, 1.944, 1, 2.056, 1, 2.167, 1, 1, 2.556, 1, 2.944, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 0, 0.333, 1, 1, 1.333, 1, 2.333, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.333, 0, 1, 0.478, 0, 0.622, 0.022, 0.767, 0.2, 1, 1.144, 0.665, 1.522, 1, 1.9, 1, 1, 1.978, 1, 2.056, 0, 2.133, 0, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "exp5", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 1, 2, 0, 2.5, 1, 3, 1, 1, 3.111, 1, 3.222, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 1, 0, 1, 1.5, 0, 2, 0.6, 2.5, 0.6, 1, 2.667, 0.6, 2.833, 0, 3, 0, 1, 3.111, 0, 3.222, 0, 3.333, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 1, 0, 1, 1.5, 0, 2, 0.7, 2.5, 0.7, 1, 2.667, 0.7, 2.833, 0, 3, 0, 1, 3.111, 0, 3.222, 0, 3.333, 0]}]}