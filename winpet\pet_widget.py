#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面伴侣主窗口类
基于DyberPet的PetWidget架构，实现透明背景、无边框窗口、鼠标拖拽等功能
"""

import os
import sys
from sys import platform
from PySide6.QtWidgets import (QWidget, QLabel, QVBoxLayout, QHBoxLayout, 
                               QSystemTrayIcon, QMenu, QApplication)
from PySide6.QtCore import Qt, QPoint, QTimer, QSize, Signal
from PySide6.QtGui import QPixmap, QCursor, QIcon, QAction

from menu_system import RightClickMenu
from tray_system import SystemTray


class PetCompanion(QWidget):
    """桌面伴侣主窗口类"""
    
    # 信号定义
    move_sig = Signal(int, int, name="move_sig")
    
    def __init__(self, parent=None, screens=None):
        """
        初始化桌面伴侣
        :param parent: 父窗口
        :param screens: 屏幕列表
        """
        super(PetCompanion, self).__init__(parent)
        
        # 屏幕信息
        self.screens = screens or []
        if self.screens:
            self.current_screen = self.screens[0].availableGeometry()
            self.screen_width = self.current_screen.width()
            self.screen_height = self.current_screen.height()
        else:
            # 默认屏幕信息
            self.current_screen = QApplication.primaryScreen().availableGeometry()
            self.screen_width = self.current_screen.width()
            self.screen_height = self.current_screen.height()
        
        # 鼠标拖拽相关属性
        self.is_follow_mouse = False
        self.mouse_drag_pos = QPoint(0, 0)
        
        # 窗口属性
        self.companion_size = QSize(200, 200)  # 默认大小
        
        # 初始化UI
        self._init_widget()
        self._init_ui()
        self._setup_menu()
        self._setup_tray()
        
        # 设置初始位置（屏幕右下角）
        self._set_initial_position()
        
    def _init_widget(self):
        """初始化窗体，设置无边框半透明窗口"""
        # 设置窗口标志
        if platform == 'win32':
            self.setWindowFlags(
                Qt.FramelessWindowHint | 
                Qt.WindowStaysOnTopHint | 
                Qt.SubWindow | 
                Qt.NoDropShadowWindowHint
            )
        else:
            # SubWindow 在 MacOS 上不工作
            self.setWindowFlags(
                Qt.FramelessWindowHint | 
                Qt.WindowStaysOnTopHint | 
                Qt.NoDropShadowWindowHint
            )
        
        # 设置透明背景
        self.setAutoFillBackground(False)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        
        # 设置窗口大小
        self.setFixedSize(self.companion_size)
        
        # 初始化拖拽状态
        self.is_follow_mouse = False
        self.mouse_drag_pos = self.pos()
        
    def _init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建显示标签（暂时用占位图片）
        self.display_label = QLabel()
        self.display_label.setAlignment(Qt.AlignCenter)
        self.display_label.setStyleSheet("""
            QLabel {
                background-color: rgba(100, 150, 200, 100);
                border: 2px solid rgba(255, 255, 255, 150);
                border-radius: 10px;
            }
        """)
        
        # 设置占位文本
        self.display_label.setText("桌面伴侣\n(开发中)")
        self.display_label.setStyleSheet("""
            QLabel {
                background-color: rgba(100, 150, 200, 100);
                border: 2px solid rgba(255, 255, 255, 150);
                border-radius: 10px;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.display_label)
        self.setLayout(layout)
        
    def _setup_menu(self):
        """设置右键菜单"""
        self.right_menu = RightClickMenu(self)
        
    def _setup_tray(self):
        """设置系统托盘"""
        self.tray = SystemTray(self)
        
    def _set_initial_position(self):
        """设置初始位置到屏幕右下角"""
        x = self.screen_width - self.width() - 50
        y = self.screen_height - self.height() - 50
        self.move(x, y)
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.RightButton:
            # 显示右键菜单
            self.right_menu.show_menu(QCursor.pos())
            
        elif event.button() == Qt.LeftButton:
            # 开始拖拽
            self.is_follow_mouse = True
            self.mouse_drag_pos = event.globalPos() - self.pos()
            event.accept()
            self.setCursor(QCursor(Qt.ClosedHandCursor))
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if Qt.LeftButton and self.is_follow_mouse:
            # 计算新位置
            new_pos = event.globalPos() - self.mouse_drag_pos
            
            # 限制在屏幕边界内
            new_pos = self._limit_to_screen(new_pos)
            
            self.move(new_pos)
            event.accept()
            
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_follow_mouse = False
            self.setCursor(QCursor(Qt.ArrowCursor))
            
    def _limit_to_screen(self, pos):
        """限制窗口位置在屏幕边界内"""
        x = max(0, min(pos.x(), self.screen_width - self.width()))
        y = max(0, min(pos.y(), self.screen_height - self.height()))
        return QPoint(x, y)
        
    def moveEvent(self, event):
        """窗口移动事件"""
        # 发射移动信号
        self.move_sig.emit(self.pos().x() + self.width() // 2, 
                          self.pos().y() + self.height())
        super().moveEvent(event)
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.setCursor(QCursor(Qt.OpenHandCursor))
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        if not self.is_follow_mouse:
            self.setCursor(QCursor(Qt.ArrowCursor))
        super().leaveEvent(event)
        
    def show_companion(self):
        """显示伴侣"""
        self.show()
        self.raise_()
        self.activateWindow()
        
    def hide_companion(self):
        """隐藏伴侣"""
        self.hide()
        
    def quit_application(self):
        """退出应用程序"""
        if self.tray:
            self.tray.hide()
        QApplication.quit()
        
    def toggle_visibility(self):
        """切换显示/隐藏状态"""
        if self.isVisible():
            self.hide_companion()
        else:
            self.show_companion()
