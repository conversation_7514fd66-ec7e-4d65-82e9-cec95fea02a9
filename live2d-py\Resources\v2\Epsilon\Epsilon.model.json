{"version": "Sample 1.0.0", "model": "Epsilon.moc", "textures": ["Epsilon.1024/texture_00.png", "Epsilon.1024/texture_01.png", "Epsilon.1024/texture_02.png"], "motions": {"idle": [{"file": "motions/Epsilon_idle_01.mtn"}], "null": [{"file": "motions/Epsilon_m_01.mtn"}, {"file": "motions/Epsilon_m_02.mtn"}, {"file": "motions/Epsilon_m_03.mtn"}, {"file": "motions/Epsilon_m_04.mtn"}, {"file": "motions/Epsilon_m_05.mtn"}, {"file": "motions/Epsilon_m_06.mtn"}, {"file": "motions/Epsilon_m_07.mtn"}, {"file": "motions/Epsilon_m_08.mtn"}, {"file": "motions/Epsilon_m_sp_01.mtn"}, {"file": "motions/Epsilon_m_sp_02.mtn"}, {"file": "motions/Epsilon_m_sp_03.mtn"}, {"file": "motions/Epsilon_m_sp_04.mtn"}, {"file": "motions/Epsilon_m_sp_05.mtn"}, {"file": "motions/Epsilon_shake_01.mtn"}]}, "expressions": [{"name": "f01.exp.json", "file": "expressions/f01.exp.json"}, {"name": "f02.exp.json", "file": "expressions/f02.exp.json"}, {"name": "f03.exp.json", "file": "expressions/f03.exp.json"}, {"name": "f04.exp.json", "file": "expressions/f04.exp.json"}, {"name": "f05.exp.json", "file": "expressions/f05.exp.json"}, {"name": "f06.exp.json", "file": "expressions/f06.exp.json"}, {"name": "f07.exp.json", "file": "expressions/f07.exp.json"}, {"name": "f08.exp.json", "file": "expressions/f08.exp.json"}], "physics": "Epsilon.physics.json"}