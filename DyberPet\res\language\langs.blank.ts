<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS><TS version="2.0">
<context>
    <name>Char<PERSON>ard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="937"/>
        <source>Character Info</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CharCardWidget</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1080"/>
        <source>Unnamed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1212"/>
        <source>Go to folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1214"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1126"/>
        <source>No description.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1160"/>
        <source>Unknown author</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CharInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="59"/>
        <source>Characters Management</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="61"/>
        <source>Collected Characters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="66"/>
        <source>Add Characters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="69"/>
        <source>Add Chars Manually</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="78"/>
        <source>Characters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="167"/>
        <source>Switch to </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="168"/>
        <source>Might take some time, just wait a moment &lt;3</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="176"/>
        <source>Loading Character...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="337"/>
        <source>Please wait patiently</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="185"/>
        <source>Launched!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="209"/>
        <source>Function incomplete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="210"/>
        <source>The function has not been implemented yet.
Currently, you can Go To Folder, delete the whole folder, and restart App.
Sorry for the inconvenience.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="387"/>
        <source>Go to Folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="237"/>
        <source>Adding Character</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="238"/>
        <source>You are about to import a character from a local file. Please be aware that it is from third-party sources. We are not responsible for any potential harm or issues that may arise from using this character. Only proceed if you trust the source.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="243"/>
        <source>Please select the character folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="266"/>
        <source>There is already a character with the same name added.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="328"/>
        <source>Adding character completed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="337"/>
        <source>Copying Files</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="346"/>
        <source>Copy complete!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="354"/>
        <source>Copying folder failed with unknown reason.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="369"/>
        <source>Adding Failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="370"/>
        <source>Success!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="371"/>
        <source>pet_conf.json broken or not exist!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="372"/>
        <source>act_conf.json broken or not exist</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="373"/>
        <source>The following actions are missing &quot;images&quot; attribute:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="374"/>
        <source>The following image files missing:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="375"/>
        <source>The following default actions missing in pet_conf.json:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="376"/>
        <source>The following actions called by pet_conf.json are missing from act_conf.json:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="385"/>
        <source>Add Characters Manually</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="386"/>
        <source>1. Prepare the character folder containing all files;
2. Copy the folder to App resource folder (you can click &apos;Go to Folder&apos; button);
3. Close App and open again;
4. You will see the character show up here;
5. Click &apos;Launch&apos; to start;
6. If App crushed, it means the character file is problematic, please contact the author for help.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="406"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/CharCardUI.py" line="409"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CharLine</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="892"/>
        <source>Launch</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ControlMainWindow</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="56"/>
        <source>Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="57"/>
        <source>Game Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="60"/>
        <source>Characters</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="63"/>
        <source>Item MOD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/DyberControlPanel.py" line="73"/>
        <source>System</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DPDialogue</name>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="3136"/>
        <source>Segoe UI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="3316"/>
        <source>Back</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DPNote</name>
    <message>
        <location filename="../../DyberPet/Notification.py" line="272"/>
        <source>&#xe5;&#xae;&#xa0;&#xe7;&#x89;&#xa9;&#xe8;&#xa6;&#x81;&#xe9;&#xa5;&#xbf;&#xe6;&#xad;&#xbb;&#xe5;&#x95;&#xa6;&#xef;&#xbc;&#x81;(&#xe5;&#xa5;&#xbd;&#xe6;&#x84;&#x9f;&#xe5;&#xba;&#xa6;&#xe5;&#xbc;&#x80;&#xe5;&#xa7;&#x8b;&#xe4;&#xb8;&#x8b;&#xe9;&#x99;&#x8d;)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Notification.py" line="274"/>
        <source>&#xe5;&#xae;&#xa0;&#xe7;&#x89;&#xa9;&#xe7;&#x8e;&#xb0;&#xe5;&#x9c;&#xa8;&#xe5;&#xbe;&#x88;&#xe9;&#xa5;&#xbf;&#xe5;&#x93;&#xa6;~ &#xef;&#xbc;&#x88;&#xe5;&#xa5;&#xbd;&#xe6;&#x84;&#x9f;&#xe5;&#xba;&#xa6;&#xe5;&#x81;&#x9c;&#xe6;&#xad;&#xa2;&#xe5;&#xa2;&#x9e;&#xe5;&#x8a;&#xa0;&#xef;&#xbc;&#x89;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Notification.py" line="279"/>
        <source>&#xe6;&#x81;&#xad;&#xe5;&#x96;&#x9c;&#xe4;&#xbd;&#xa0;&#xef;&#xbc;&#x81;&#xe5;&#xa5;&#xbd;&#xe6;&#x84;&#x9f;&#xe5;&#xba;&#xa6;&#xe5;&#xb7;&#xb2;&#xe8;&#xbe;&#xbe;&#xe4;&#xb8;&#x8a;&#xe9;&#x99;&#x90;&#xef;&#xbc;&#x81;&#xe6;&#x84;&#x9f;&#xe8;&#xb0;&#xa2;&#xe8;&#xbf;&#x99;&#xe4;&#xb9;&#x88;&#xe4;&#xb9;&#x85;&#xe4;&#xbb;&#xa5;&#xe6;&#x9d;&#xa5;&#xe7;&#x9a;&#x84;&#xe9;&#x99;&#xaa;&#xe4;&#xbc;&#xb4;&#xef;&#xbc;&#x81;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>DashboardMainWindow</name>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="54"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="57"/>
        <source>Backpack</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="60"/>
        <source>Shop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="63"/>
        <source>Daily Tasks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/DashboardUI.py" line="73"/>
        <source>Dashboard</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>EmptyTaskCard</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2953"/>
        <source>Add New Task</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FVWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="435"/>
        <source>Favor</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Focus</name>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="970"/>
        <source>&#xe4;&#xb8;&#x93;&#xe6;&#xb3;&#xa8;&#xe6;&#x97;&#xb6;&#xe9;&#x97;&#xb4;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="995"/>
        <source>&#xe6;&#x8c;&#x81;&#xe7;&#xbb;&#xad;&#xe4;&#xb8;&#x80;&#xe6;&#xae;&#xb5;&#xe6;&#x97;&#xb6;&#xe9;&#x97;&#xb4;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="997"/>
        <source>&#xe5;&#xae;&#x9a;&#xe6;&#x97;&#xb6;&#xe7;&#xbb;&#x93;&#xe6;&#x9d;&#x9f;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1032"/>
        <source>&#xe5;&#xb0;&#x8f;&#xe6;&#x97;&#xb6;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1036"/>
        <source>&#xe5;&#x88;&#x86;&#xe9;&#x92;&#x9f;&#xe5;&#x90;&#x8e;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1069"/>
        <source>&#xe5;&#x88;&#xb0;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1073"/>
        <source>&#xe7;&#x82;&#xb9;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1077"/>
        <source>&#xe5;&#x88;&#x86;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1231"/>
        <source>&#xe5;&#xbc;&#x80;&#xe5;&#xa7;&#x8b;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1089"/>
        <source>&#xe5;&#x81;&#x9c;&#xe6;&#xad;&#xa2;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1176"/>
        <source>&#xe7;&#xbb;&#xa7;&#xe7;&#xbb;&#xad;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1219"/>
        <source>&#xe6;&#x9a;&#x82;&#xe5;&#x81;&#x9c;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>FocusPanel</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1963"/>
        <source>Focus Period</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2201"/>
        <source>Lauch Focus</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2002"/>
        <source>Break by Pomodoro</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2083"/>
        <source>You will not have break time.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2023"/>
        <source>Start</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2029"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2059"/>
        <source>Usage Help</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2059"/>
        <source>Please set up a period to focus on the work/study.

Once this focus task is done, you will get coin rewarded.

Even if you stopped the clock in the middle, you will still get rewarded accordingly.

Choose &apos;Break by Pomodoro&apos; will adjust the time to fit closest number of pomodoro.
Everytime you finish a 25min Pomodoro, you get coin rewarded</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2080"/>
        <source>You will take a 5-minute break every 25 minutes.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2120"/>
        <source>Pomodoro Time Remaining</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2142"/>
        <source>Focus Time Remaining</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2239"/>
        <source>Focus task reward:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>HPWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="357"/>
        <source>Satiety</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Interaction_worker</name>
    <message>
        <location filename="../../DyberPet/modules.py" line="455"/>
        <source>needs Satiety be larger than</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Inventory</name>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="2334"/>
        <source>&#xe4;&#xbd;&#xbf;&#xe7;&#x94;&#xa8;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="2168"/>
        <source>&#xe5;&#x85;&#xb3;&#xe9;&#x97;&#xad;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="2180"/>
        <source>&#xe5;&#xae;&#xa0;&#xe7;&#x89;&#xa9;&#xe8;&#x83;&#x8c;&#xe5;&#x8c;&#x85;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="2332"/>
        <source>&#xe6;&#x94;&#xb6;&#xe5;&#x9b;&#x9e;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ItemCard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1337"/>
        <source>Item MOD Info</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ItemCardWidget</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1477"/>
        <source>Unnamed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1521"/>
        <source>No description.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1555"/>
        <source>Unknown author</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ItemInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="59"/>
        <source>Item MOD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="61"/>
        <source>Collected Item MOD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="66"/>
        <source>Add Items</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="69"/>
        <source>Add Items Manually</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="78"/>
        <source>Items</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="192"/>
        <source>Function incomplete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="193"/>
        <source>The function has not been implemented yet.
Currently, you can Go To Folder, delete the whole folder, and restart App.
Sorry for the inconvenience.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="342"/>
        <source>Go to Folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="214"/>
        <source>Adding Item MOD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="215"/>
        <source>You are about to import a item MOD from a local file. Please be aware that it is from third-party sources. We are not responsible for any potential harm or issues that may arise from using this MOD. Only proceed if you trust the source.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="220"/>
        <source>Please select the MOD folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="243"/>
        <source>There is already a MOD with the same folder name.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="285"/>
        <source>Adding item MOD completed! Please restart App to apply MOD.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="294"/>
        <source>Copying Files</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="294"/>
        <source>Please wait patiently</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="303"/>
        <source>Copy complete!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="311"/>
        <source>Copying folder failed with unknown reason.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="326"/>
        <source>Adding Failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="327"/>
        <source>Success!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="328"/>
        <source>items_config.json broken or not exist.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="329"/>
        <source>&apos;image&apos; key missing:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="330"/>
        <source>The following items are missing image files:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="331"/>
        <source>In the following items, &apos;pet_limit&apos; is not a list:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="340"/>
        <source>Add Item MOD Manually</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="341"/>
        <source>1. Prepare the MOD folder containing all files;
2. Copy the folder to App resource folder (you can click &apos;Go to Folder&apos; button);
3. Close App and open again;
4. You will see the MOD show up here;
 *If the MOD not shown or App crushed, it means the MOD file has unexpected error, please contact the author for help.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="361"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/ItemCardUI.py" line="364"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ItemLine</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="1292"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PetWidget</name>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="712"/>
        <source>More Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="724"/>
        <source>Select Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1551"/>
        <source>Follow Cursor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="746"/>
        <source>Call Partner</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="791"/>
        <source>Default Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="807"/>
        <source>Change Character</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1562"/>
        <source>Allow Drop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1558"/>
        <source>Don&apos;t Drop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="834"/>
        <source>Website</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1055"/>
        <source>Exit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="952"/>
        <source> (Fed for </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="953"/>
        <source> days)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1063"/>
        <source>Status: </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1336"/>
        <source>Satiety</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1003"/>
        <source>Favor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1055"/>
        <source>Dashboard</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1055"/>
        <source>System</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1338"/>
        <source>Favorability</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberPet.py" line="1529"/>
        <source>Stop Follow</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ProgressPanel</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2292"/>
        <source>Daily Goal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2322"/>
        <source>Yesterday</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2493"/>
        <source>Minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2347"/>
        <source>Progress</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2375"/>
        <source>Daily Goal: 180 Minutes</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2396"/>
        <source>Completed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2402"/>
        <source>Days in a row</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2419"/>
        <source>Set The Goal</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2420"/>
        <source>Upon reaching your set time, you&apos;ll be rewarded with coins. Earn extra rewards by completing certain streaks of consecutive days!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2493"/>
        <source>Daily Goal:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2535"/>
        <source>Daily Goal Completed:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2539"/>
        <source>days in a row reward:</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QAccessory</name>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="412"/>
        <source>Withdraw</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QToaster</name>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="2800"/>
        <source>Segoe UI</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QuickSaveCard</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="440"/>
        <source>Load In</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="444"/>
        <source>Rewrite</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="448"/>
        <source>Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_utils.py" line="452"/>
        <source>Backtrace</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Remindme</name>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1306"/>
        <source>&#xe4;&#xb8;&#x80;&#xe6;&#xae;&#xb5;&#xe6;&#x97;&#xb6;&#xe9;&#x97;&#xb4;&#xe5;&#x90;&#x8e;&#xe6;&#x8f;&#x90;&#xe9;&#x86;&#x92;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1308"/>
        <source>&#xe5;&#xae;&#x9a;&#xe6;&#x97;&#xb6;&#xe6;&#x8f;&#x90;&#xe9;&#x86;&#x92;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1310"/>
        <source>&#xe9;&#x97;&#xb4;&#xe9;&#x9a;&#x94;&#xe9;&#x87;&#x8d;&#xe5;&#xa4;&#x8d;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1318"/>
        <source>&#xe6;&#x8f;&#x90;&#xe9;&#x86;&#x92;&#xe4;&#xba;&#x8b;&#xe9;&#xa1;&#xb9;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1354"/>
        <source>&#xe5;&#xb0;&#x8f;&#xe6;&#x97;&#xb6;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1358"/>
        <source>&#xe5;&#x88;&#x86;&#xe9;&#x92;&#x9f;&#xe5;&#x90;&#x8e;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1384"/>
        <source>&#xe5;&#x88;&#xb0;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1388"/>
        <source>&#xe7;&#x82;&#xb9;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1392"/>
        <source>&#xe5;&#x88;&#x86;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1398"/>
        <source>&#xe5;&#x9c;&#xa8;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1400"/>
        <source>&#xe6;&#xaf;&#x8f;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1415"/>
        <source>&#xe5;&#x88;&#x86;&#xe6;&#x97;&#xb6;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1428"/>
        <source>&#xe5;&#x88;&#x86;&#xe9;&#x92;&#x9f;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1439"/>
        <source>&#xe7;&#xa1;&#xae;&#xe5;&#xae;&#x9a;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1521"/>
        <source>&#xe5;&#xae;&#x8b;&#xe4;&#xbd;&#x93;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1473"/>
        <source>&#xe6;&#x8f;&#x90;&#xe9;&#x86;&#x92;&#xe6;&#x88;&#x91;&#xef;&#xbc;&#x9a;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1497"/>
        <source>&#xe5;&#xa4;&#x87;&#xe5;&#xbf;&#x98;&#xe5;&#xbd;&#x95;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1498"/>
        <source>&#xe5;&#xa4;&#x87;&#xe5;&#xbf;&#x98;&#xe5;&#xbd;&#x95;&#xe8;&#x87;&#xaa;&#xe5;&#x8a;&#xa8;&#xe4;&#xbf;&#x9d;&#xe5;&#xad;&#x98;&#xef;&#xbc;&#x8c;
&#xe4;&#xb8;&#x8b;&#xe6;&#xac;&#xa1;&#xe6;&#x89;&#x93;&#xe5;&#xbc;&#x80;&#xe6;&#x97;&#xb6;&#xe8;&#x87;&#xaa;&#xe5;&#x8a;&#xa8;&#xe8;&#xbd;&#xbd;&#xe5;&#x85;&#xa5;&#xe5;&#x86;&#x85;&#xe5;&#xae;&#xb9;&#xe5;&#x92;&#x8c;&#xe6;&#x8f;&#x90;&#xe9;&#x86;&#x92;&#xe4;&#xba;&#x8b;&#xe9;&#xa1;&#xb9;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1693"/>
        <source>#&#xe9;&#x87;&#x8d;&#xe5;&#xa4;&#x8d;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1598"/>
        <source>&#xe6;&#xaf;&#x8f;&#xe5;&#x88;&#xb0;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="1601"/>
        <source>&#xe6;&#xaf;&#x8f;&#xe9;&#x9a;&#x94;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SaveInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="54"/>
        <source>Game Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="58"/>
        <source>Save Transfer</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="67"/>
        <source>Choose folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="67"/>
        <source>Export to</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="75"/>
        <source>Import for</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="75"/>
        <source>Import from</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="261"/>
        <source>All pets</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="75"/>
        <source>Please choose the pet, then choose the save folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="83"/>
        <source>Select pet, then select folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="109"/>
        <source>Quick Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="202"/>
        <source>Choose Export folder</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="218"/>
        <source>Export Succeed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="220"/>
        <source>Export Failed! Please try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="229"/>
        <source>Are you sure you want to import another save?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="230"/>
        <source>&#xe2;&#x80;&#xa2; Make sure you have exported the current save, in case an error happened
&#xe2;&#x80;&#xa2; Currently, only save will be imported, note and settings won&apos;t be influenced
&#xe2;&#x80;&#xa2; Only selected character save will be modified</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="238"/>
        <source>Choose Save Folder to Import</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="255"/>
        <source>File: pet_data.json not found in selected folder!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="265"/>
        <source>File: pet_data.json is not in compatible format!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="277"/>
        <source>Save imported successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="280"/>
        <source>Failed to import save!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="290"/>
        <source>Name of the Save</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="454"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="455"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="329"/>
        <source>Save Succeed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="331"/>
        <source>Save Failed! Please try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="448"/>
        <source>Updating Save card failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="350"/>
        <source>Load in the save?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="351"/>
        <source>Pet save data will be overwritten.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="421"/>
        <source>Error: Save folder in bad format!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="379"/>
        <source>Are you sure you want to delete the save?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="380"/>
        <source>All history saves in this slot will be deleted, use carefully</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="389"/>
        <source>Deletion Succeed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="391"/>
        <source>Error: Deletion Failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="404"/>
        <source>Are you sure you want to backtrace the save slot?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="405"/>
        <source>It will delete the current save, and backtrace to the last one in this slot.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="426"/>
        <source>Save backtraced successfully!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/GameSaveUI.py" line="428"/>
        <source>Error: Deleting current save Failed!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Scheduler_worker</name>
    <message>
        <location filename="../../DyberPet/modules.py" line="734"/>
        <source>*Setting config file broken. Setting is re-initialized.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="740"/>
        <source>*Game save file broken. Data is re-initialized.
Please load previous saved data to recover.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="772"/>
        <source>Good Morning!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="776"/>
        <source>Good Afternoon!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="778"/>
        <source>Good Evening!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/modules.py" line="782"/>
        <source>Time to sleep!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SettingInterface</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="49"/>
        <source>Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="71"/>
        <source>Mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="73"/>
        <source>Always-On-Top</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="73"/>
        <source>Pet will be displayed on top of the other Apps</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="85"/>
        <source>Allow Drop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="85"/>
        <source>When mouse released, pet falls to the ground (on) / stays at the site (off)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="99"/>
        <source>Interaction</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="100"/>
        <source>Gravity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="100"/>
        <source>Pet falling down acceleration</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="111"/>
        <source>Drag Speed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="111"/>
        <source>Mouse speed factor</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="124"/>
        <source>Volumn</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="124"/>
        <source>Volumn of notification and pet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="135"/>
        <source>Personalization</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="136"/>
        <source>Pet Scale</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="136"/>
        <source>Adjust size of the pet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="147"/>
        <source>Default Pet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="147"/>
        <source>Pet to show everytime App starts</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="161"/>
        <source>Language/&#xe8;&#xaf;&#xad;&#xe8;&#xa8;&#x80;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="161"/>
        <source>Set your preferred language for UI</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="172"/>
        <source>About</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="173"/>
        <source>Open GitHub Page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="173"/>
        <source>Project Website</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="173"/>
        <source>Check update and learn more about the project on our GitHub page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="181"/>
        <source>Open Issue Page</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="181"/>
        <source>Help &amp; Issue</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="181"/>
        <source>Post your issue or question on our GitHub Issue, or contact us on BiliBili</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="189"/>
        <source>Open Developer Document</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="189"/>
        <source>Re-development</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="189"/>
        <source>If you want to develop your own pet/item/actions... Check here</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/BasicSettingUI.py" line="302"/>
        <source>Configuration takes effect after restart
&#xe6;&#xad;&#xa4;&#xe8;&#xae;&#xbe;&#xe7;&#xbd;&#xae;&#xe5;&#x9c;&#xa8;&#xe9;&#x87;&#x8d;&#xe5;&#x90;&#xaf;&#xe5;&#x90;&#x8e;&#xe7;&#x94;&#x9f;&#xe6;&#x95;&#x88;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ShopItemWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1388"/>
        <source>Sell</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ShopMessageBox</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1781"/>
        <source>Buy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1783"/>
        <source>Sell</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1795"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ShopView</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1558"/>
        <source>Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="1561"/>
        <source>MOD</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>StatusCard</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="281"/>
        <source> (Fed for </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="282"/>
        <source> days)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SubPet</name>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1210"/>
        <source>Select Action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1255"/>
        <source>Allow Drop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1251"/>
        <source>Don&apos;t Drop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Accessory.py" line="1242"/>
        <source>Exit</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TaskPanel</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2675"/>
        <source>Tasks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2692"/>
        <source>Completed </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2696"/>
        <source> tasks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2733"/>
        <source>On-Going</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2745"/>
        <source>Completed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2844"/>
        <source>Task completed! </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="2847"/>
        <source>You completed another 5 tasks! </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Tomato</name>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="916"/>
        <source>&#xe5;&#xbc;&#x80;&#xe5;&#xa7;&#x8b;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="796"/>
        <source>&#xe6;&#xac;&#xa1;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="827"/>
        <source>&#xe7;&#xa1;&#xae;&#xe5;&#xae;&#x9a;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="831"/>
        <source>&#xe5;&#x81;&#x9c;&#xe6;&#xad;&#xa2;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/extra_windows.py" line="902"/>
        <source>&#xe6;&#xad;&#xa3;&#xe5;&#x9c;&#xa8;&#xe8;&#xbf;&#x9b;&#xe8;&#xa1;&#x8c;&#xe7;&#xac;&#xac;</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>Ui_SaveNameDialog</name>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_base.py" line="41"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/DyberSettings/custom_base.py" line="42"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>animationInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="35"/>
        <source>Animations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/animationUI.py" line="36"/>
        <source>All Animations</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>backpackInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="68"/>
        <source>Backpack</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="267"/>
        <source>Use</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="223"/>
        <source>Backpack Guide</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="224"/>
        <source>Backpack keeps all the items pet got.

There are in total 3 tabs and the coins display:
    - Consumable items (food, etc.)
    - Collections (Dialogue, etc.)
    - Subpet
(All tabs have infinite volume.)

Items have different effects, such as adding HP. Some of them also have Buff effects. Please position your cursor over the item to see details.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="240"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="243"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="267"/>
        <source>Withdraw</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/inventoryUI.py" line="295"/>
        <source>Randomly dropped</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>coinWidget</name>
    <message>
        <location filename="../../DyberPet/Dashboard/dashboard_widgets.py" line="734"/>
        <source>Dyber Coin</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>shopInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="53"/>
        <source>Shop</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="72"/>
        <source>Filter</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Food</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Collection</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="100"/>
        <source>Pet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="103"/>
        <source>MOD</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="110"/>
        <source>Search by name, MOD...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="199"/>
        <source>Shop Guide</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="200"/>
        <source>Not Implemented</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="208"/>
        <source>OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="211"/>
        <source>Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/shopUI.py" line="274"/>
        <source>One Char can have only one </source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>statusInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="55"/>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../DyberPet/Dashboard/statusUI.py" line="58"/>
        <source>Status Log</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>taskInterface</name>
    <message>
        <location filename="../../DyberPet/Dashboard/taskUI.py" line="34"/>
        <source>Daily Tasks</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
