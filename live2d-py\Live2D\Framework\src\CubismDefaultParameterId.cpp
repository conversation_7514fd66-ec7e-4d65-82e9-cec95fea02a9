﻿/**
 * Copyright(c) Live2D Inc. All rights reserved.
 *
 * Use of this source code is governed by the Live2D Open Software license
 * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.
 */

#include "CubismDefaultParameterId.hpp"

//--------- LIVE2D NAMESPACE ------------
namespace Live2D { namespace Cubism { namespace Framework { namespace DefaultParameterId {

// パーツID
const csmChar* HitAreaPrefix = "HitArea";
const csmChar* HitAreaHead = "Head";
const csmChar* HitAreaBody = "Body";
const csmChar* PartsIdCore = "Parts01Core";
const csmChar* PartsArmPrefix = "Parts01Arm_";
const csmChar* PartsArmLPrefix = "Parts01ArmL_";
const csmChar* PartsArmRPrefix = "Parts01ArmR_";

// パラメータID
const csmChar* ParamAngleX = "ParamAngleX";
const csmChar* ParamAngleY = "ParamAngleY";
const csmChar* ParamAngleZ = "ParamAngleZ";
const csmChar* ParamEyeLOpen = "ParamEyeLOpen";
const csmChar* ParamEyeLSmile = "ParamEyeLSmile";
const csmChar* ParamEyeROpen = "ParamEyeROpen";
const csmChar* ParamEyeRSmile = "ParamEyeRSmile";
const csmChar* ParamEyeBallX = "ParamEyeBallX";
const csmChar* ParamEyeBallY = "ParamEyeBallY";
const csmChar* ParamEyeBallForm = "ParamEyeBallForm";
const csmChar* ParamBrowLY = "ParamBrowLY";
const csmChar* ParamBrowRY = "ParamBrowRY";
const csmChar* ParamBrowLX = "ParamBrowLX";
const csmChar* ParamBrowRX = "ParamBrowRX";
const csmChar* ParamBrowLAngle = "ParamBrowLAngle";
const csmChar* ParamBrowRAngle = "ParamBrowRAngle";
const csmChar* ParamBrowLForm = "ParamBrowLForm";
const csmChar* ParamBrowRForm = "ParamBrowRForm";
const csmChar* ParamMouthForm = "ParamMouthForm";
const csmChar* ParamMouthOpenY = "ParamMouthOpenY";
const csmChar* ParamCheek = "ParamCheek";
const csmChar* ParamBodyAngleX = "ParamBodyAngleX";
const csmChar* ParamBodyAngleY = "ParamBodyAngleY";
const csmChar* ParamBodyAngleZ = "ParamBodyAngleZ";
const csmChar* ParamBreath = "ParamBreath";
const csmChar* ParamArmLA = "ParamArmLA";
const csmChar* ParamArmRA = "ParamArmRA";
const csmChar* ParamArmLB = "ParamArmLB";
const csmChar* ParamArmRB = "ParamArmRB";
const csmChar* ParamHandL = "ParamHandL";
const csmChar* ParamHandR = "ParamHandR";
const csmChar* ParamHairFront = "ParamHairFront";
const csmChar* ParamHairSide = "ParamHairSide";
const csmChar* ParamHairBack = "ParamHairBack";
const csmChar* ParamHairFluffy = "ParamHairFluffy";
const csmChar* ParamShoulderY = "ParamShoulderY";
const csmChar* ParamBustX = "ParamBustX";
const csmChar* ParamBustY = "ParamBustY";
const csmChar* ParamBaseX = "ParamBaseX";
const csmChar* ParamBaseY = "ParamBaseY";
const csmChar* ParamNONE = "NONE:";
}}}}

//--------- LIVE2D NAMESPACE ------------
