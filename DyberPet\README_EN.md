<h1 align="center">
  呆啵宠物  |  DyberPet
</h1>

<p align="center">
  DyberPet is a PySide6-based Desktop Cyber Pet Framework, providing an App for all desktop pet creators
</p>

<p align="center">
  <a>
    <img src="https://img.shields.io/github/license/ChaozhongLiu/DyberPet.svg">
  </a>

  <a style="text-decoration:none">
    <img src="https://img.shields.io/github/downloads/ChaozhongLiu/DyberPet/total.svg"/>
  </a>

  <a style="text-decoration:none">
    <img src="https://img.shields.io/badge/python-3.9+-blue.svg" />
  </a>

  <a style="text-decoration:none">
    <img src="https://img.shields.io/badge/DyberPet-v0.6.7-green.svg"/>
  </a>
</p>

<p align="center">
English | <a href="README.md">简体中文</a>
</p>

:octocat: The project is at the very early stage, and mostly maintained in Chinese. Please leave an issue here if you have any suggestion, question, or trouble using it.  
  
:new: **08-22-2024: v0.5.7** App has been packaged and submit to [Release](https://github.com/ChaozhongLiu/DyberPet/releases/tag/v0.5.7). Any of your feedback is more than welcomed!  
  
:new: **04-06-2024: v0.3.7** has been adapted to PySide6-Fluent-Widgets v1.5.4, Please update PySide6-Fluent-Widgets with pip to run DyberPet.  
  
🆕 **Language changer** is released now, supporting English and Simplified Chinese.    
  
⭐ Please **STAR** if you like it and want to get the update!


## Try the Demo
### Windows Users
  Download the latest Release，double-click **``run_DyberPet.exe``**, that's it!

### Windows Terminal
  Create a new **conda** environment 
  ```
  conda create --name Dyber_pyside python=3.9.18
  conda activate Dyber_pyside
  conda install -c conda-forge apscheduler
  conda install -c conda-forge pynput
  pip install PySide6-Fluent-Widgets==1.5.4 -i https://pypi.org/simple/
  pip install pyside6==6.5.2
  pip install tendo
  ```
  Download the repository，then run **``run_DyberPet.py``**.
  
### MacOS Users
  Create a new **conda** environment  
  ```
  conda create --name Dyber_pyside python=3.9.18
  conda activate Dyber_pyside
  conda install -c conda-forge apscheduler
  pip install pynput==1.7.6
  pip install PySide6-Fluent-Widgets==1.5.4 -i https://pypi.org/simple/
  pip install pyside6==6.5.2
  pip install tendo
  ```
  Download the repository，then run **``run_DyberPet.py``**.




## User Manual
(Under construction)




## Developer Manual
(English version under construction)



## Acknowledgement
- Pictures in the Demo partially come from [daywa1kr](https://github.com/daywa1kr/Desktop-Cat)
- Animation module reference: [yanji255](https://toscode.gitee.com/yanji255/desktop_pet/)  
- Dragging and falling reference: [WolfChen1996](https://github.com/WolfChen1996/DesktopPet)

