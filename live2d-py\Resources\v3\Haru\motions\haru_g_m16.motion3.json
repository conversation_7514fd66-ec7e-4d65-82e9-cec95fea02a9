{"Version": 3, "Meta": {"Duration": 4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 63, "TotalSegmentCount": 247, "TotalPointCount": 638, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 1.32, 1, 2.64, 1, 3.97, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 2, 1, 0.333, 2, 0.667, 2, 1, 2, 1, 1.133, 2, 1.267, 2, 1.4, 2, 1, 1.511, 2, 1.622, 2.132, 1.733, 1.634, 1, 1.878, 0.986, 2.022, -4, 2.167, -4, 1, 2.367, -4, 2.567, 0, 2.767, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 3, 1, 0.267, 3, 0.533, 3, 0.8, 3, 1, 0.878, 3, 0.956, -4, 1.033, -4, 1, 1.189, -4, 1.344, 2.269, 1.5, 11, 1, 1.611, 17.237, 1.722, 19, 1.833, 19, 1, 1.978, 19, 2.122, -28, 2.267, -28, 1, 2.467, -28, 2.667, -23, 2.867, -23, 0, 4, -23]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -7, 1, 0.4, -7, 0.8, -7, 1.2, -7, 1, 1.333, -7, 1.467, 19, 1.6, 19, 1, 1.711, 19, 1.822, 19.636, 1.933, 16.192, 1, 2.078, 11.714, 2.222, -27, 2.367, -27, 1, 2.567, -27, 2.767, -23, 2.967, -23, 0, 4, -23]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.5, 1.4, 0.5, 1, 1.511, 0.5, 1.622, 0.5, 1.733, 0.5, 1, 1.878, 0.5, 2.022, 0.5, 2.167, 0.5, 0, 4, 0.5]}, {"Target": "Parameter", "Id": "ParamFaceForm", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.133, 1, 1.267, 1.6, 1.4, 1.6, 1, 1.511, 1.6, 1.622, 1.502, 1.733, 1.502, 1, 1.767, 1.502, 1.8, 1.8, 1.833, 1.8, 1, 1.944, 1.8, 2.056, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.511, 0, 1.622, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.133, 1, 1.267, 1.6, 1.4, 1.6, 1, 1.511, 1.6, 1.622, 1.502, 1.733, 1.502, 1, 1.767, 1.502, 1.8, 1.8, 1.833, 1.8, 1, 1.944, 1.8, 2.056, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.511, 0, 1.622, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0.015, 1.733, 0.186, 1, 1.878, 0.286, 2.022, 0.6, 2.167, 0.6, 0, 4, 0.6]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.511, 0, 1.622, -0.023, 1.733, 0.061, 1, 1.878, 0.171, 2.022, 1, 2.167, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0, 1.4, 0, 1, 1.511, 0, 1.622, 0.031, 1.733, 0.031, 1, 1.878, 0.031, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, -0.2, 1.4, -0.2, 1, 1.511, -0.2, 1.622, -0.157, 1.733, -0.157, 1, 1.878, -0.157, 2.022, -0.4, 2.167, -0.4, 0, 4, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.7, 1.4, 0.7, 1, 1.511, 0.7, 1.622, 0.735, 1.733, 0.608, 1, 1.878, 0.444, 2.022, -0.8, 2.167, -0.8, 0, 4, -0.8]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.7, 1.4, 0.7, 1, 1.511, 0.7, 1.622, 0.735, 1.733, 0.608, 1, 1.878, 0.444, 2.022, -0.8, 2.167, -0.8, 0, 4, -0.8]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.1, 1.4, 0.1, 1, 1.511, 0.1, 1.622, 0.112, 1.733, 0.069, 1, 1.878, 0.015, 2.022, -0.4, 2.167, -0.4, 0, 4, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.1, 1.4, 0.1, 1, 1.511, 0.1, 1.622, 0.112, 1.733, 0.069, 1, 1.878, 0.015, 2.022, -0.4, 2.167, -0.4, 0, 4, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.7, 1.4, 0.7, 1, 1.511, 0.7, 1.622, 0.4, 1.733, 0.4, 1, 1.878, 0.4, 2.022, 0.8, 2.167, 0.8, 0, 4, 0.8]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.7, 1.4, 0.7, 1, 1.511, 0.7, 1.622, 0.4, 1.733, 0.4, 1, 1.878, 0.4, 2.022, 0.8, 2.167, 0.8, 0, 4, 0.8]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, -0.451, 1.4, -0.7, 1, 1.511, -0.908, 1.622, -0.9, 1.733, -0.9, 1, 1.878, -0.9, 2.022, -0.8, 2.167, -0.8, 0, 4, -0.8]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, -0.451, 1.4, -0.7, 1, 1.511, -0.908, 1.622, -0.9, 1.733, -0.9, 1, 1.878, -0.9, 2.022, -0.8, 2.167, -0.8, 0, 4, -0.8]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.133, 1, 1.267, -0.37, 1.4, -0.4, 1, 1.511, -0.425, 1.622, -0.417, 1.733, -0.435, 1, 1.878, -0.459, 2.022, -0.5, 2.167, -0.5, 0, 4, -0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 0.4, 1.4, 0.4, 1, 1.511, 0.4, 1.622, 0.409, 1.733, 0.376, 1, 1.878, 0.332, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamScarf", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.322, 0, 0.644, 0, 0.967, 0, 1, 1.1, 0, 1.233, 5, 1.367, 5, 1, 1.478, 5, 1.589, 5.238, 1.7, 4.267, 1, 1.844, 3.005, 1.989, -7, 2.133, -7, 1, 2.333, -7, 2.533, -6, 2.733, -6, 0, 4, -6]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, 3, 1.4, 3, 1, 1.511, 3, 1.622, 3.254, 1.733, 2.206, 1, 1.878, 0.845, 2.022, -10, 2.167, -10, 1, 2.367, -10, 2.567, -9, 2.767, -9, 0, 4, -9]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0, 1.167, 0, 1, 1.3, 0, 1.433, 2, 1.567, 2, 1, 1.678, 2, 1.789, 2.188, 1.9, 1.451, 1, 2.044, 0.491, 2.189, -7, 2.333, -7, 1, 2.533, -7, 2.733, -6, 2.933, -6, 0, 4, -6]}, {"Target": "Parameter", "Id": "ParamBodyUpper", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.133, 0, 1.267, -3, 1.4, -3, 1, 1.511, -3, 1.622, -3.069, 1.733, -2.817, 1, 1.878, -2.489, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.244, 1, 1.489, 1, 1.733, 1, 1, 1.878, 1, 2.022, 1, 2.167, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.244, 1, 1.489, 1, 1.733, 1, 1, 1.878, 1, 2.022, 1, 2.167, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 2.7, 1, 0.333, 2.7, 0.667, 2.7, 1, 2.7, 1, 1.133, 2.7, 1.267, 4.3, 1.4, 4.3, 1, 1.511, 4.3, 1.622, 4.356, 1.733, 4.19, 1, 1.922, 3.907, 2.111, 2.5, 2.3, 2.5, 0, 4, 2.5]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 2.7, 1, 0.333, 2.7, 0.667, 2.7, 1, 2.7, 1, 1.133, 2.7, 1.267, 4.3, 1.4, 4.3, 1, 1.511, 4.3, 1.622, 4.356, 1.733, 4.19, 1, 1.922, 3.907, 2.111, 2.5, 2.3, 2.5, 0, 4, 2.5]}, {"Target": "Parameter", "Id": "ParamHandChangeR", "Segments": [0, 0.5, 1, 0.333, 0.5, 0.667, 0.5, 1, 0.5, 1, 1.311, 0.5, 1.622, 0.5, 1.933, 0.5, 1, 2.178, 0.5, 2.422, 0.5, 2.667, 0.5, 0, 4, 0.5]}, {"Target": "Parameter", "Id": "ParamHandAngleR", "Segments": [0, 0.3, 1, 0.333, 0.3, 0.667, 0.3, 1, 0.3, 1, 1.078, 0.3, 1.156, 1, 1.233, 1, 1, 1.356, 1, 1.478, 0.4, 1.6, 0.4, 1, 1.711, 0.4, 1.822, 0.387, 1.933, 0.327, 1, 2.178, 0.255, 2.422, 0.2, 2.667, 0.2, 0, 4, 0.2]}, {"Target": "Parameter", "Id": "ParamHandDhangeL", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.311, 0, 1.622, 0, 1.933, 0, 1, 2.178, 0, 2.422, 0, 2.667, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleL", "Segments": [0, -0.17, 1, 0.333, -0.17, 0.667, -0.17, 1, -0.17, 1, 1.078, -0.17, 1.156, -1, 1.233, -1, 1, 1.356, -1, 1.478, -0.4, 1.6, -0.4, 1, 1.711, -0.4, 1.822, -0.379, 1.933, -0.333, 1, 2.178, -0.262, 2.422, -0.2, 2.667, -0.2, 0, 4, -0.2]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.244, 0, 1.489, 0, 1.733, 0, 1, 1.878, 0, 2.022, 0, 2.167, 0, 0, 4, 0]}, {"Target": "PartOpacity", "Id": "Part01Core", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Hoho001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Brow001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Tear", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01EyeBall001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Eye001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Nose001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Mouth001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Face001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Ear001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Neck001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01HairFront001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01HairSide001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01HairBack001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRB001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmLB001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRA001", "Segments": [0, 0, 2, 3.97, 0, 0, 4, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmLA001", "Segments": [0, 0, 2, 3.97, 0, 0, 4, 0]}, {"Target": "PartOpacity", "Id": "Part01Body001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Sketch", "Segments": [0, 0, 2, 3.97, 0, 0, 4, 0]}]}