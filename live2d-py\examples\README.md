# Live2D Python Examples 使用指南

本文档将指导您如何使用 live2d-py 的各种示例代码，以及如何将它们集成到您自己的项目中。

## 📋 目录

- [环境要求](#环境要求)
- [快速开始](#快速开始)
- [示例分类](#示例分类)
  - [基础示例](#基础示例)
  - [GUI框架集成](#gui框架集成)
  - [高级功能](#高级功能)
  - [面部捕捉](#面部捕捉)
- [项目结构](#项目结构)
- [常见问题](#常见问题)
- [性能优化](#性能优化)

## 🔧 环境要求

### 基本要求
- **Python**: 3.8+ (推荐 3.9+)
- **操作系统**: Windows 10+, Linux (Ubuntu 18.04+), macOS 10.15+
- **OpenGL**: 3.3+ 支持

### 依赖包
```bash
# 核心依赖
pip install live2d-py

# 示例依赖 (根据需要选择安装)
pip install -r requirements.txt
```

### GPU 要求
- 支持 OpenGL 3.3+ 的显卡
- 推荐独立显卡以获得更好性能

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装 live2d-py (选择其中一种方式)
# 方式1: 从 PyPI 安装
pip install live2d-py

# 方式2: 从 Release 下载 whl 文件安装 (推荐)
pip install live2d_py-0.X.X-cpXXX-cpXXX-win32.whl

# 安装示例所需的额外依赖
pip install -r requirements.txt
```

### 2. 准备模型文件

创建 `Resources` 目录并放置您的 Live2D 模型：

```
Resources/
├── v2/                    # Cubism 2.x 模型
│   ├── kasumi2/
│   ├── haru/
│   └── 托尔/
└── v3/                    # Cubism 3.x+ 模型
    ├── Haru/
    ├── llny/
    └── 小九/
```

### 3. 运行示例

```bash
# 运行最简单的示例
python main_pygame_simple.py

# 运行完整功能示例
python main_pygame.py

# 运行面部捕捉示例
python main_facial_bind.py
```

### 4. 集成到您的项目

复制以下文件到您的项目：
- `resources.py` - 资源路径配置
- `Resources/` - 模型文件目录
- 选择合适的示例代码作为起点

## 📚 示例分类

### 基础示例

#### `main_pygame_simple.py` - 最简单的示例
**适用场景**: 初学者入门，快速验证环境
**功能特点**:
- 最小化代码实现
- 支持 Cubism 2.x 和 3.x 模型
- 基本的模型加载和渲染

```python
# 核心代码结构
import pygame
import live2d.v3 as live2d  # 或 live2d.v2
import resources

# 初始化
pygame.init()
live2d.init()
live2d.glewInit()

# 加载模型
model = live2d.LAppModel()
model.LoadModelJson("path/to/model.json")

# 渲染循环
while running:
    live2d.clearBuffer()
    model.Update()
    model.Draw()
    pygame.display.flip()
```

#### `main_pygame.py` - 完整功能示例
**适用场景**: 了解所有基础功能
**功能特点**:
- 模型变换 (缩放、旋转、平移)
- 音频口型同步
- 鼠标交互和点击检测
- 动作播放和回调
- 参数控制

### GUI框架集成

#### Pygame 系列

##### `main_pygame_background.py` - 背景图片
**功能**: 演示如何添加背景图片
```python
from live2d.utils.image import Image
background = Image("background.png")
```

##### `main_pygame_three_model.py` - 多模型渲染
**功能**: 同时渲染多个 Live2D 模型
**特点**:
- 支持 v2 和 v3 模型混合
- 独立的模型控制
- 性能优化示例

##### `main_pygame_fine_grained.py` - 精细控制
**功能**: 精确的模型参数控制和部件操作

#### Qt 系列

##### `main_pyqt5_canvas_opacity.py` - 透明度控制
**功能**: 模型整体透明度动画
**适用场景**: 需要透明效果的应用

##### `main_pyqt5_canvas_opacity_rotation.py` - 旋转效果
**功能**: 模型旋转动画 (仅 Cubism 3.x)

##### `main_pyqt5_multi_win.py` - 多窗口
**功能**: 多窗口中分别显示不同模型

##### `main_pyside6.py` / `main_pyside2.py` - PySide 集成
**功能**: PySide 框架集成示例

##### `main_pyside6_moderngl_qt.py` - ModernGL 集成
**功能**: 结合 ModernGL 和 Qt 的高级渲染

#### 其他框架

##### `main_glfw.py` - GLFW 集成
**适用场景**: 需要更底层 OpenGL 控制
**特点**:
- 轻量级窗口管理
- 直接 OpenGL 操作
- 适合游戏开发

##### `main_tkinter.py` - Tkinter 集成
**适用场景**: 简单的桌面应用
**特点**:
- 使用 pyopengltk
- 鼠标跟踪
- 透明窗口效果

### 高级功能

#### `motion_fix.py` - 动作修复工具
**功能**: 修复损坏的动作文件
**使用场景**: 模型动作文件出现问题时

### 面部捕捉

#### `main_facial_bind.py` - 实时面部捕捉
**功能**: 使用 MediaPipe 进行实时面部追踪
**特点**:
- 实时面部表情捕捉
- 眼部、嘴部、头部姿态控制
- 平滑算法减少抖动
- 支持摄像头输入

**安装额外依赖**:
```bash
pip install mediapipe opencv-python
```

**使用方法**:
```python
# 启动面部捕捉
python main_facial_bind.py
```

**支持的参数**:
- `ParamEyeLOpen` / `ParamEyeROpen` - 左右眼开合
- `ParamMouthOpenY` - 嘴部开合
- `ParamMouthForm` - 嘴部形状
- `ParamAngleX/Y/Z` - 头部姿态
- `ParamEyeBallX` - 眼球位置

#### MediaPipe 捕捉模块

位于 `mediapipe_capture/` 目录：

- `new_capture_task.py` - 捕捉任务主程序
- `facial_params.py` - 面部参数计算
- `calculation.py` - 数学计算工具
- `math_utils.py` - 数学工具函数
- `face_landmarker.task` - MediaPipe 模型文件

**自定义面部捕捉**:
```python
from mediapipe_capture.facial_params import Params
from mediapipe_capture.new_capture_task import capture_task

# 创建参数对象
params = Params()

# 启动捕捉线程
import threading
thread = threading.Thread(target=capture_task, args=(params, 0), daemon=True)
thread.start()

# 在渲染循环中应用参数
model.SetParameterValue(StandardParams.ParamEyeLOpen, params.EyeLOpen, 1)
```

## 📁 项目结构

### 推荐的项目结构

```
your_project/
├── main.py                 # 主程序
├── resources.py            # 资源路径配置
├── requirements.txt        # 依赖列表
├── Resources/              # 模型资源目录
│   ├── v2/                # Cubism 2.x 模型
│   │   ├── kasumi2/
│   │   │   ├── kasumi2.model.json
│   │   │   ├── kasumi2.moc
│   │   │   ├── textures/
│   │   │   └── motions/
│   │   └── ...
│   ├── v3/                # Cubism 3.x+ 模型
│   │   ├── Haru/
│   │   │   ├── Haru.model3.json
│   │   │   ├── Haru.moc3
│   │   │   ├── textures/
│   │   │   └── motions/
│   │   └── ...
│   └── backgrounds/       # 背景图片 (可选)
├── audio/                 # 音频文件 (可选)
└── mediapipe_capture/     # 面部捕捉模块 (如需要)
```

### resources.py 配置

```python
import os

CURRENT_DIRECTORY = os.path.split(__file__)[0]
RESOURCES_DIRECTORY = os.path.join(CURRENT_DIRECTORY, "Resources")

# 可选：添加具体模型路径
HARU_MODEL_PATH = os.path.join(RESOURCES_DIRECTORY, "v3/Haru/Haru.model3.json")
KASUMI_MODEL_PATH = os.path.join(RESOURCES_DIRECTORY, "v2/kasumi2/kasumi2.model.json")
```

## ❓ 常见问题

### 安装问题

**Q: 安装 live2d-py 时出现编译错误**
A:
1. 确保使用预编译的 whl 文件而不是源码安装
2. 检查 Python 版本是否支持 (3.8+)
3. 从 [Release](https://github.com/Arkueid/live2d-py/releases) 下载对应平台的 whl 文件

**Q: 导入 live2d 模块失败**
A:
```python
# 检查安装
import live2d
print(live2d.__version__)

# 如果失败，尝试重新安装
pip uninstall live2d-py
pip install live2d_py-0.X.X-cpXXX-cpXXX-win32.whl
```

### 运行问题

**Q: OpenGL 相关错误**
A:
1. 确保显卡驱动最新
2. 检查 OpenGL 版本: `glxinfo | grep "OpenGL version"` (Linux)
3. 尝试在代码中添加：
```python
import os
os.environ['MESA_GL_VERSION_OVERRIDE'] = '3.3'
```

**Q: 模型加载失败**
A:
1. 检查模型文件路径是否正确
2. 确认模型文件完整性
3. 检查模型版本 (v2 vs v3) 与导入的模块匹配

**Q: 面部捕捉不工作**
A:
1. 确保摄像头权限已开启
2. 检查 MediaPipe 安装：`pip install mediapipe opencv-python`
3. 确认 `face_landmarker.task` 文件存在

### 性能问题

**Q: 帧率过低**
A:
1. 降低窗口分辨率
2. 关闭不必要的功能：
```python
model.SetAutoBreathEnable(False)  # 关闭自动呼吸
model.SetAutoBlinkEnable(False)   # 关闭自动眨眼
```
3. 使用独立显卡而非集成显卡

**Q: 内存占用过高**
A:
1. 及时释放不用的模型：
```python
del model
live2d.dispose()
```
2. 避免同时加载过多模型

## ⚡ 性能优化

### 渲染优化

```python
# 1. 设置合适的帧率
pygame.time.wait(int(1000 / 60))  # 60 FPS

# 2. 优化缓冲区清理
live2d.clearBuffer()  # 每帧调用一次即可

# 3. 批量参数更新
model.SetParameterValue(param1, value1, weight1)
model.SetParameterValue(param2, value2, weight2)
model.Update()  # 统一更新
```

### 内存优化

```python
# 1. 模型预加载
models = {}
for name, path in model_paths.items():
    models[name] = live2d.LAppModel()
    models[name].LoadModelJson(path)

# 2. 纹理共享 (相同模型)
# live2d-py 自动处理纹理共享

# 3. 及时清理
def cleanup():
    for model in models.values():
        del model
    live2d.dispose()
```

### 多线程优化

```python
import threading
import queue

# 面部捕捉在独立线程
def capture_thread(param_queue):
    # 捕捉逻辑
    while running:
        params = capture_face()
        param_queue.put(params)

# 主线程渲染
param_queue = queue.Queue()
thread = threading.Thread(target=capture_thread, args=(param_queue,))
thread.daemon = True
thread.start()

# 渲染循环
while running:
    if not param_queue.empty():
        params = param_queue.get()
        apply_params(model, params)

    model.Update()
    model.Draw()
```

## 📖 更多资源

- **官方文档**: [Wiki](https://github.com/Arkueid/live2d-py/wiki)
- **API 参考**: [API Documentation](https://github.com/Arkueid/live2d-py/wiki/API)
- **问题反馈**: [GitHub Issues](https://github.com/Arkueid/live2d-py/issues)
- **QQ 交流群**: 783375488

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

参考 [CONTRIBUTING.md](../CONTRIBUTING.md) 了解如何参与开发。

---

**注意**: 本项目仅供学习和研究使用，请遵守 Live2D 的使用条款。
