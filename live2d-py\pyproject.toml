[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
requires-python = ">=3.2"
dynamic = ["version", "license"]
name = "live2d-py"
dependencies = [
    "pyopengl",
    "pillow",
    "numpy"
]
authors = [
    { name = "Arkue<PERSON>", email = "<EMAIL>" }
]
description = "A non-web Live2D library for Python."
readme = "README.md"
keywords = ["Live2D", "Cubism Live2D", "Cubism SDK", "Cubism SDK for Python"]

[project.urls]
Homepage = "https://github.com/Arkueid/live2d-py"
Documentation = "https://Arkueid.github.io/live2d-py-docs/"
Repository = "https://github.com/Arkueid/live2d-py"
