{"Version": 3, "Meta": {"Duration": 5.333, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 88, "TotalSegmentCount": 404, "TotalPointCount": 1082, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 1, 0, 5.333, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 2.167, 0, 1, 2.278, 0, 2.389, -16, 2.5, -16, 1, 2.833, -16, 3.167, -16, 3.5, -16, 1, 3.722, -16, 3.944, -16, 4.167, -16, 1, 4.244, -16, 4.322, 0, 4.4, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 2.167, 0, 1, 2.278, 0, 2.389, -30, 2.5, -30, 1, 2.833, -30, 3.167, -30, 3.5, -30, 1, 3.722, -30, 3.944, -30, 4.167, -30, 1, 4.244, -30, 4.322, 0, 4.4, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 2.167, 0, 1, 2.278, 0, 2.389, -30, 2.5, -30, 1, 2.833, -30, 3.167, -29, 3.5, -29, 1, 3.722, -29, 3.944, -29, 4.167, -29, 1, 4.244, -29, 4.322, 0, 4.4, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.722, 0, 1.444, 0, 2.167, 0, 1, 2.344, 0, 2.522, -9.547, 2.7, -9.547, 1, 2.867, -9.547, 3.033, -8.488, 3.2, -8.488, 1, 3.3, -8.488, 3.4, -8.542, 3.5, -8.543, 1, 3.722, -8.543, 3.944, -8.543, 4.167, -8.543, 1, 4.311, -8.543, 4.456, 0.994, 4.6, 0.994, 1, 4.678, 0.994, 4.756, 0.259, 4.833, 0.259, 0, 5.333, 0.259]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.722, 0, 1.444, 0, 2.167, 0, 1, 2.344, 0, 2.522, -9.547, 2.7, -9.547, 1, 2.867, -9.547, 3.033, -8.488, 3.2, -8.488, 1, 3.3, -8.488, 3.4, -8.542, 3.5, -8.543, 1, 3.722, -8.543, 3.944, -8.543, 4.167, -8.543, 1, 4.311, -8.543, 4.456, 0.994, 4.6, 0.994, 1, 4.678, 0.994, 4.756, 0.259, 4.833, 0.259, 0, 5.333, 0.259]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 1, 1.6, 0, 3.2, 9, 4.8, 9, 0, 5.333, 9]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 0, 2.5, 0, 1, 2.556, 0, 2.611, 1, 2.667, 1, 1, 2.944, 1, 3.222, 1, 3.5, 1, 1, 3.722, 1, 3.944, 1, 4.167, 1, 1, 4.244, 1, 4.322, 0, 4.4, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.333, 1, 1, 0.389, 1, 0.444, 0, 0.5, 0, 1, 0.556, 0, 0.611, 1, 0.667, 1, 1, 0.722, 1, 0.778, 0, 0.833, 0, 1, 0.944, 0, 1.056, 2, 1.167, 2, 1, 1.444, 2, 1.722, 1.768, 2, 1, 1, 2.056, 0.846, 2.111, 0, 2.167, 0, 1, 2.222, 0, 2.278, 1, 2.333, 1, 1, 2.389, 1, 2.444, 0, 2.5, 0, 1, 2.611, 0, 2.722, 0.569, 2.833, 0.7, 1, 3, 0.897, 3.167, 0.9, 3.333, 0.9, 1, 3.444, 0.9, 3.556, 0.8, 3.667, 0.8, 1, 3.722, 0.8, 3.778, 1, 3.833, 1, 1, 3.889, 1, 3.944, 0.9, 4, 0.9, 0, 5.333, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.333, 1, 1, 0.389, 1, 0.444, 0, 0.5, 0, 1, 0.556, 0, 0.611, 1, 0.667, 1, 1, 0.722, 1, 0.778, 0, 0.833, 0, 1, 0.944, 0, 1.056, 2, 1.167, 2, 1, 1.444, 2, 1.722, 1.768, 2, 1, 1, 2.056, 0.846, 2.111, 0, 2.167, 0, 1, 2.222, 0, 2.278, 1, 2.333, 1, 1, 2.389, 1, 2.444, 0, 2.5, 0, 1, 2.611, 0, 2.722, 0.569, 2.833, 0.7, 1, 3, 0.897, 3.167, 0.9, 3.333, 0.9, 1, 3.444, 0.9, 3.556, 0.897, 3.667, 0.8, 1, 3.722, 0.752, 3.778, 0, 3.833, 0, 1, 3.889, 0, 3.944, 0.9, 4, 0.9, 0, 5.333, 0.9]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 2.333, 0, 1, 2.5, 0, 2.667, -1, 2.833, -1, 1, 2.944, -1, 3.056, -1, 3.167, -1, 1, 3.444, -1, 3.722, 0.8, 4, 0.8, 1, 4.111, 0.8, 4.222, 0, 4.333, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 2.333, 0, 1, 2.5, 0, 2.667, 0.7, 2.833, 0.7, 1, 2.944, 0.7, 3.056, 0.714, 3.167, 0.6, 1, 3.444, 0.314, 3.722, 0, 4, 0, 1, 4.111, 0, 4.222, 0, 4.333, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 1.667, 0, 1, 1.778, 0, 1.889, 0, 2, 0, 1, 2.111, 0, 2.222, 1, 2.333, 1, 1, 2.5, 1, 2.667, 1, 2.833, 1, 1, 3.278, 1, 3.722, 1, 4.167, 1, 1, 4.278, 1, 4.389, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 1.667, 0, 1, 1.778, 0, 1.889, 0, 2, 0, 1, 2.111, 0, 2.222, 1, 2.333, 1, 1, 2.5, 1, 2.667, -1, 2.833, -1, 1, 3.278, -1, 3.722, -1, 4.167, -1, 1, 4.278, -1, 4.389, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp1", "Segments": [0, 0, 0, 0.067, 0, 1, 0.933, 0, 1.8, 0, 2.667, 0, 1, 2.722, 0, 2.778, -0.9, 2.833, -0.9, 1, 3.056, -0.9, 3.278, -0.9, 3.5, -0.9, 1, 3.722, -0.9, 3.944, -0.9, 4.167, -0.9, 1, 4.278, -0.9, 4.389, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp2", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp11", "Segments": [0, 0, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 1, 3.056, 0, 3.278, -1, 3.5, -1, 1, 3.722, -1, 3.944, -1, 4.167, -1, 1, 4.278, -1, 4.389, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp3", "Segments": [0, 0, 0, 0.067, 0, 1, 0.378, 0, 0.689, 0, 1, 0, 1, 1.111, 0, 1.222, 0, 1.333, 0, 1, 1.556, 0, 1.778, 0, 2, 0, 1, 2.111, 0, 2.222, 0, 2.333, 0, 1, 2.5, 0, 2.667, 0, 2.833, 0, 1, 3.056, 0, 3.278, 0, 3.5, 0, 1, 3.722, 0, 3.944, 0, 4.167, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp4", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp5", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp6", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp7", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp8", "Segments": [0, 0, 0, 0.067, 0, 1, 1.511, 0, 2.956, 0, 4.4, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "exp9", "Segments": [0, 0, 0, 0.067, 0, 1, 0.878, 0, 1.689, 0, 2.5, 0, 1, 2.611, 0, 2.722, 0.879, 2.833, 0.9, 1, 3.267, 0.983, 3.7, 1, 4.133, 1, 1, 4.222, 1, 4.311, 0, 4.4, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 1, 1.611, 0, 3.222, 0, 4.833, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0.4, 0, 0.067, 0.4, 1, 0.178, 0.4, 0.289, -0.2, 0.4, -0.2, 1, 0.456, -0.2, 0.511, 0.4, 0.567, 0.4, 1, 0.733, 0.4, 0.9, -0.2, 1.067, -0.2, 1, 1.244, -0.2, 1.422, 0.4, 1.6, 0.4, 1, 1.767, 0.4, 1.933, -0.2, 2.1, -0.2, 1, 2.256, -0.2, 2.411, 0.4, 2.567, 0.4, 1, 2.733, 0.4, 2.9, -0.2, 3.067, -0.2, 1, 3.544, -0.2, 4.022, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 0.067, 0, 1, 0.156, 0, 0.244, 1, 0.333, 1, 1, 0.444, 1, 0.556, -1, 0.667, -1, 1, 0.733, -1, 0.8, 0, 0.867, 0, 1, 1.3, 0, 1.733, 0, 2.167, 0, 1, 2.389, 0, 2.611, -0.583, 2.833, -0.7, 1, 3.056, -0.817, 3.278, -0.8, 3.5, -0.8, 1, 3.722, -0.8, 3.944, -0.8, 4.167, -0.8, 1, 4.278, -0.8, 4.389, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 0, 0.067, 0, 1, 0.767, 0, 1.467, 0, 2.167, 0, 1, 2.389, 0, 2.611, -0.682, 2.833, -0.8, 1, 3.056, -0.918, 3.278, -0.9, 3.5, -0.9, 1, 3.722, -0.9, 3.944, -0.9, 4.167, -0.9, 1, 4.278, -0.9, 4.389, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 0.067, 0, 1, 0.322, 0, 0.578, 0, 0.833, 0, 1, 0.944, 0, 1.056, -4, 1.167, -4, 1, 1.5, -4, 1.833, -4, 2.167, -4, 1, 2.222, -4, 2.278, -4, 2.333, -4, 1, 2.5, -4, 2.667, -1, 2.833, -1, 1, 3.056, -1, 3.278, -1, 3.5, -1, 1, 3.722, -1, 3.944, -1, 4.167, -1, 1, 4.278, -1, 4.389, 0, 4.5, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 1, 1.611, 0, 3.222, 0, 4.833, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 0.067, 0, 1, 0.244, 0, 0.422, 0, 0.6, 0, 1, 0.711, 0, 0.822, 1, 0.933, 1, 1, 1.044, 1, 1.156, -0.6, 1.267, -0.6, 1, 1.378, -0.6, 1.489, 0.3, 1.6, 0.3, 1, 1.733, 0.3, 1.867, -0.9, 2, -0.9, 1, 2.133, -0.9, 2.267, 1.2, 2.4, 1.2, 1, 2.544, 1.2, 2.689, -0.8, 2.833, -0.8, 1, 2.944, -0.8, 3.056, 0, 3.167, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 0.067, 0, 1, 0.467, 0, 0.867, 0, 1.267, 0, 1, 1.378, 0, 1.489, 0.3, 1.6, 0.3, 1, 1.733, 0.3, 1.867, -0.9, 2, -0.9, 1, 2.133, -0.9, 2.267, 1.2, 2.4, 1.2, 1, 2.544, 1.2, 2.689, 0.273, 2.833, 0.1, 1, 2.944, -0.033, 3.056, 0, 3.167, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 0, 5.333, 0]}, {"Target": "Parameter", "Id": "waitaozuo1", "Segments": [0, -2.954, 1, 0.044, -2.954, 0.089, -2.954, 0.133, -2.954, 1, 0.144, -2.954, 0.156, -2.954, 0.167, -2.954, 1, 0.833, -2.954, 1.5, -2.954, 2.167, -2.954, 1, 2.267, -2.954, 2.367, -4.83, 2.467, -4.83, 1, 2.578, -4.83, 2.689, -1.544, 2.8, -1.544, 1, 2.933, -1.544, 3.067, -3.502, 3.2, -3.502, 1, 3.4, -3.502, 3.6, -1.022, 3.8, -1.022, 1, 3.911, -1.022, 4.022, -4.379, 4.133, -4.379, 1, 4.178, -4.379, 4.222, -3.77, 4.267, -3.77, 0, 5.333, -3.77]}, {"Target": "Parameter", "Id": "waitaozuo5", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.001, 0.133, 0.001, 1, 0.144, 0.001, 0.156, 0.001, 0.167, 0.001, 1, 0.278, 0.001, 0.389, 0, 0.5, 0, 1, 0.533, 0, 0.567, 0, 0.6, 0, 1, 0.633, 0, 0.667, 0, 0.7, 0, 1, 0.711, 0, 0.722, 0, 0.733, 0, 1, 0.756, 0, 0.778, 0, 0.8, 0, 1, 0.822, 0, 0.844, 0, 0.867, 0, 1, 0.878, 0, 0.889, 0, 0.9, 0, 1, 0.922, 0, 0.944, 0, 0.967, 0, 1, 0.978, 0, 0.989, 0, 1, 0, 1, 1.022, 0, 1.044, 0, 1.067, 0, 1, 1.078, 0, 1.089, 0, 1.1, 0, 1, 1.144, 0, 1.189, 0, 1.233, 0, 1, 1.244, 0, 1.256, 0, 1.267, 0, 1, 1.289, 0, 1.311, 0, 1.333, 0, 1, 1.344, 0, 1.356, 0, 1.367, 0, 1, 1.633, 0, 1.9, 0, 2.167, 0, 1, 2.244, 0, 2.322, 0.839, 2.4, 0.839, 1, 2.489, 0.839, 2.578, -1.763, 2.667, -1.763, 1, 2.789, -1.763, 2.911, 1.609, 3.033, 1.609, 1, 3.156, 1.609, 3.278, -1.01, 3.4, -1.01, 1, 3.467, -1.01, 3.533, -0.24, 3.6, -0.24, 1, 3.633, -0.24, 3.667, -0.333, 3.7, -0.333, 1, 3.8, -0.333, 3.9, 1.648, 4, 1.648, 1, 4.089, 1.648, 4.178, -1.256, 4.267, -1.256, 0, 5.333, -1.256]}, {"Target": "Parameter", "Id": "waitaozuo6", "Segments": [0, -2.953, 1, 0.1, -2.953, 0.2, -2.954, 0.3, -2.954, 1, 0.356, -2.954, 0.411, -2.954, 0.467, -2.954, 1, 0.478, -2.954, 0.489, -2.954, 0.5, -2.954, 1, 0.511, -2.954, 0.522, -2.954, 0.533, -2.954, 1, 0.544, -2.954, 0.556, -2.954, 0.567, -2.954, 1, 0.589, -2.954, 0.611, -2.954, 0.633, -2.954, 1, 0.644, -2.954, 0.656, -2.954, 0.667, -2.954, 1, 0.733, -2.954, 0.8, -2.954, 0.867, -2.954, 1, 0.878, -2.954, 0.889, -2.954, 0.9, -2.954, 1, 1.322, -2.954, 1.744, -2.954, 2.167, -2.954, 1, 2.267, -2.954, 2.367, -13.014, 2.467, -13.014, 1, 2.578, -13.014, 2.689, 4.536, 2.8, 4.536, 1, 2.933, 4.536, 3.067, -5.86, 3.2, -5.86, 1, 3.4, -5.86, 3.6, 7.152, 3.8, 7.152, 1, 3.911, 7.152, 4.022, -10.553, 4.133, -10.553, 1, 4.178, -10.553, 4.222, -7.142, 4.267, -7.142, 0, 5.333, -7.142]}, {"Target": "Parameter", "Id": "waitaozuo7", "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.006, 0.133, 0.006, 1, 0.144, 0.006, 0.156, 0.006, 0.167, 0.006, 1, 0.289, 0.006, 0.411, -0.002, 0.533, -0.002, 1, 0.644, -0.002, 0.756, 0.001, 0.867, 0.001, 1, 0.878, 0.001, 0.889, 0.001, 0.9, 0.001, 1, 0.911, 0.001, 0.922, 0.001, 0.933, 0.001, 1, 1.033, 0.001, 1.133, 0, 1.233, 0, 1, 1.278, 0, 1.322, 0, 1.367, 0, 1, 1.378, 0, 1.389, 0, 1.4, 0, 1, 1.411, 0, 1.422, 0, 1.433, 0, 1, 1.444, 0, 1.456, 0, 1.467, 0, 1, 1.478, 0, 1.489, 0, 1.5, 0, 1, 1.511, 0, 1.522, 0, 1.533, 0, 1, 1.544, 0, 1.556, 0, 1.567, 0, 1, 1.578, 0, 1.589, 0, 1.6, 0, 1, 1.678, 0, 1.756, 0, 1.833, 0, 1, 1.844, 0, 1.856, 0, 1.867, 0, 1, 1.967, 0, 2.067, 0, 2.167, 0, 1, 2.244, 0, 2.322, 4.49, 2.4, 4.49, 1, 2.489, 4.49, 2.578, -9.374, 2.667, -9.374, 1, 2.789, -9.374, 2.911, 8.555, 3.033, 8.555, 1, 3.156, 8.555, 3.278, -5.345, 3.4, -5.345, 1, 3.467, -5.345, 3.533, -1.301, 3.6, -1.301, 1, 3.633, -1.301, 3.667, -1.798, 3.7, -1.798, 1, 3.8, -1.798, 3.9, 9.075, 4, 9.075, 1, 4.089, 9.075, 4.178, -6.996, 4.267, -6.996, 0, 5.333, -6.996]}, {"Target": "Parameter", "Id": "waitaozuo4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.044, 0, 0.056, 0, 0.067, 0, 1, 0.144, 0, 0.222, 2.448, 0.3, 2.448, 1, 0.378, 2.448, 0.456, -5.096, 0.533, -5.096, 1, 0.644, -5.096, 0.756, 3.658, 0.867, 3.658, 1, 1, 3.658, 1.133, -3.014, 1.267, -3.014, 1, 1.444, -3.014, 1.622, 2.508, 1.8, 2.508, 1, 1.956, 2.508, 2.111, -2.467, 2.267, -2.467, 1, 2.422, -2.467, 2.578, 2.268, 2.733, 2.268, 1, 2.889, 2.268, 3.044, -1.528, 3.2, -1.528, 1, 3.322, -1.528, 3.444, 0.236, 3.567, 0.236, 1, 3.644, 0.236, 3.722, -0.573, 3.8, -0.573, 1, 3.911, -0.573, 4.022, 0.522, 4.133, 0.522, 1, 4.178, 0.522, 4.222, 0.284, 4.267, 0.284, 0, 5.333, 0.284]}, {"Target": "Parameter", "Id": "waitaozuo2", "Segments": [0, -0.004, 1, 0.022, -0.004, 0.044, -0.002, 0.067, -0.002, 1, 0.122, -0.002, 0.178, -1.103, 0.233, -1.103, 1, 0.333, -1.103, 0.433, 4.206, 0.533, 4.206, 1, 0.611, 4.206, 0.689, -4.824, 0.767, -4.824, 1, 0.878, -4.824, 0.989, 4.196, 1.1, 4.196, 1, 1.233, 4.196, 1.367, -3.224, 1.5, -3.224, 1, 1.644, -3.224, 1.789, 2.119, 1.933, 2.119, 1, 2.111, 2.119, 2.289, -2.031, 2.467, -2.031, 1, 2.622, -2.031, 2.778, 1.731, 2.933, 1.731, 1, 3.078, 1.731, 3.222, -1.53, 3.367, -1.53, 1, 3.489, -1.53, 3.611, 1.096, 3.733, 1.096, 1, 3.844, 1.096, 3.956, -0.863, 4.067, -0.863, 1, 4.133, -0.863, 4.2, 0.17, 4.267, 0.17, 0, 5.333, 0.17]}, {"Target": "Parameter", "Id": "waitaozuo3", "Segments": [0, -0.011, 1, 0.122, -0.011, 0.244, -0.557, 0.367, -0.557, 1, 0.456, -0.557, 0.544, 2.154, 0.633, 2.154, 1, 0.733, 2.154, 0.833, -3.267, 0.933, -3.267, 1, 1.044, -3.267, 1.156, 3.673, 1.267, 3.673, 1, 1.4, 3.673, 1.533, -3.438, 1.667, -3.438, 1, 1.8, -3.438, 1.933, 2.665, 2.067, 2.665, 1, 2.233, 2.665, 2.4, -2.046, 2.567, -2.046, 1, 2.722, -2.046, 2.878, 1.757, 3.033, 1.757, 1, 3.189, 1.757, 3.344, -1.545, 3.5, -1.545, 1, 3.622, -1.545, 3.744, 1.197, 3.867, 1.197, 1, 3.989, 1.197, 4.111, -0.809, 4.233, -0.809, 1, 4.244, -0.809, 4.256, -0.78, 4.267, -0.78, 0, 5.333, -0.78]}, {"Target": "Parameter", "Id": "waitaoyou1", "Segments": [0, -0.001, 1, 0.1, -0.001, 0.2, 2.319, 0.3, 2.319, 1, 0.378, 2.319, 0.456, -4.829, 0.533, -4.829, 1, 0.644, -4.829, 0.756, 3.467, 0.867, 3.467, 1, 1, 3.467, 1.133, -2.856, 1.267, -2.856, 1, 1.444, -2.856, 1.622, 2.376, 1.8, 2.376, 1, 1.933, 2.376, 2.067, -1.807, 2.2, -1.807, 1, 2.289, -1.807, 2.378, 9.931, 2.467, 9.931, 1, 2.589, 9.931, 2.711, -5.751, 2.833, -5.751, 1, 2.956, -5.751, 3.078, 1.582, 3.2, 1.582, 1, 3.4, 1.582, 3.6, -11.124, 3.8, -11.124, 1, 3.911, -11.124, 4.022, 8.32, 4.133, 8.32, 1, 4.178, 8.32, 4.222, 4.828, 4.267, 4.828, 0, 5.333, 4.828]}, {"Target": "Parameter", "Id": "waitaoyou2", "Segments": [0, -0.001, 1, 0.078, -0.001, 0.156, -1.05, 0.233, -1.05, 1, 0.333, -1.05, 0.433, 3.985, 0.533, 3.985, 1, 0.611, 3.985, 0.689, -4.574, 0.767, -4.574, 1, 0.878, -4.574, 0.989, 3.978, 1.1, 3.978, 1, 1.233, 3.978, 1.367, -3.056, 1.5, -3.056, 1, 1.644, -3.056, 1.789, 2.008, 1.933, 2.008, 1, 2.089, 2.008, 2.244, -5.879, 2.4, -5.879, 1, 2.5, -5.879, 2.6, 8.697, 2.7, 8.697, 1, 2.811, 8.697, 2.922, -6.749, 3.033, -6.749, 1, 3.156, -6.749, 3.278, 3.656, 3.4, 3.656, 1, 3.456, 3.656, 3.511, 1.757, 3.567, 1.757, 1, 3.611, 1.757, 3.656, 2.793, 3.7, 2.793, 1, 3.8, 2.793, 3.9, -9.258, 4, -9.258, 1, 4.089, -9.258, 4.178, 6.506, 4.267, 6.506, 0, 5.333, 6.506]}, {"Target": "Parameter", "Id": "waitaoyou3", "Segments": [0, 0.033, 1, 0.122, 0.033, 0.244, -0.547, 0.367, -0.547, 1, 0.456, -0.547, 0.544, 2.048, 0.633, 2.048, 1, 0.733, 2.048, 0.833, -3.097, 0.933, -3.097, 1, 1.044, -3.097, 1.156, 3.482, 1.267, 3.482, 1, 1.4, 3.482, 1.533, -3.261, 1.667, -3.261, 1, 1.8, -3.261, 1.933, 2.527, 2.067, 2.527, 1, 2.222, 2.527, 2.378, -4.162, 2.533, -4.162, 1, 2.644, -4.162, 2.756, 6.442, 2.867, 6.442, 1, 2.978, 6.442, 3.089, -6.268, 3.2, -6.268, 1, 3.322, -6.268, 3.444, 4.478, 3.567, 4.478, 1, 3.756, 4.478, 3.944, -6.59, 4.133, -6.59, 1, 4.178, -6.59, 4.222, -2.378, 4.267, -2.378, 0, 5.333, -2.378]}]}