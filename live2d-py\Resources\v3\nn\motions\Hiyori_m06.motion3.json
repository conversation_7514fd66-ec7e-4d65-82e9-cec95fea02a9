{"Curves": [{"Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 1, 0.333, 1, 1, 0.7, 1, 1.067, 0, 1.433, 0, 1, 1.711, 0, 1.989, 10, 2.267, 10, 1, 2.522, 10, 2.778, -7, 3.033, -7, 1, 3.233, -7, 3.433, 13, 3.633, 13, 1, 3.811, 13, 3.989, 7.017, 4.167, 2, 1, 4.244, -0.195, 4.322, 0, 4.4, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 18, 0.333, 18, 1, 0.522, 18, 0.711, -9.95, 0.9, -9.95, 1, 0.956, -9.95, 1.011, -1.664, 1.067, -1.589, 1, 1.911, -0.462, 2.756, 0, 3.6, 0, 1, 3.789, 0, 3.978, -18, 4.167, -18, 1, 4.244, -18, 4.322, -10, 4.4, -10, 0, 5.367, -10], "Target": "Parameter"}, {"Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.489, 0, 0.644, 25, 0.8, 25, 1, 0.878, 25, 0.956, 25, 1.033, 25, 1, 1.233, 25, 1.433, 25, 1.633, 25, 1, 1.867, 25, 2.1, -5, 2.333, -5, 1, 2.533, -5, 2.733, 23, 2.933, 23, 1, 3.189, 23, 3.444, -3, 3.7, -3, 0, 5.367, -3], "Target": "Parameter"}, {"Id": "ParamCheek", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 1, 0.533, 1, 1, 0.567, 1, 0.6, 0, 0.633, 0, 1, 0.644, 0, 0.656, 0, 0.667, 0, 1, 0.7, 0, 0.733, 1, 0.767, 1, 1, 1.056, 1, 1.344, 1, 1.633, 1, 1, 1.667, 1, 1.7, 0, 1.733, 0, 1, 1.744, 0, 1.756, 0, 1.767, 0, 1, 1.8, 0, 1.833, 1, 1.867, 1, 1, 2.433, 1, 3, 1, 3.567, 1, 1, 3.6, 1, 3.633, 0, 3.667, 0, 1, 3.678, 0, 3.689, 0, 3.7, 0, 1, 3.733, 0, 3.767, 1, 3.8, 1, 1, 3.944, 1, 4.089, 1, 4.233, 1, 1, 4.256, 1, 4.278, 0, 4.3, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 1.378, 0, 2.756, 0, 4.133, 0, 1, 4.167, 0, 4.2, 0.833, 4.233, 0.833, 0, 5.367, 0.833], "Target": "Parameter"}, {"Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.178, 1, 0.356, 1, 0.533, 1, 1, 0.567, 1, 0.6, 0, 0.633, 0, 1, 0.644, 0, 0.656, 0, 0.667, 0, 1, 0.7, 0, 0.733, 1, 0.767, 1, 1, 1.056, 1, 1.344, 1, 1.633, 1, 1, 1.667, 1, 1.7, 0, 1.733, 0, 1, 1.744, 0, 1.756, 0, 1.767, 0, 1, 1.8, 0, 1.833, 1, 1.867, 1, 1, 2.433, 1, 3, 1, 3.567, 1, 1, 3.6, 1, 3.633, 0, 3.667, 0, 1, 3.678, 0, 3.689, 0, 3.7, 0, 1, 3.733, 0, 3.767, 0.998, 3.8, 1, 1, 3.944, 1.01, 4.089, 1.013, 4.233, 1.013, 1, 4.256, 1.013, 4.278, 0, 4.3, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 1.378, 0, 2.756, 0, 4.133, 0, 1, 4.167, 0, 4.2, 0.823, 4.233, 0.823, 0, 5.367, 0.823], "Target": "Parameter"}, {"Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.122, 0, 0.244, -0.067, 0.367, -0.14, 1, 0.5, -0.22, 0.633, -0.24, 0.767, -0.24, 1, 0.856, -0.24, 0.944, -0.24, 1.033, -0.24, 1, 1.311, -0.24, 1.589, -0.24, 1.867, -0.25, 1, 2.022, -0.256, 2.178, -0.51, 2.333, -0.51, 1, 2.578, -0.51, 2.822, 0.22, 3.067, 0.22, 1, 3.267, 0.22, 3.467, -0.57, 3.667, -0.57, 1, 3.911, -0.57, 4.156, -0.28, 4.4, -0.28, 0, 5.367, -0.28], "Target": "Parameter"}, {"Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.122, 0, 0.244, -0.38, 0.367, -0.38, 1, 0.5, -0.38, 0.633, 0.21, 0.767, 0.21, 1, 0.856, 0.21, 0.944, 0.21, 1.033, 0.21, 1, 1.311, 0.21, 1.589, 0.29, 1.867, 0.29, 1, 2.022, 0.29, 2.178, 0.21, 2.333, 0.21, 1, 2.578, 0.21, 2.822, 0.29, 3.067, 0.29, 1, 3.267, 0.29, 3.467, 0.08, 3.667, 0.08, 1, 3.911, 0.08, 4.156, 0.13, 4.4, 0.13, 0, 5.367, 0.13], "Target": "Parameter"}, {"Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.078, 0, 0.156, -0.008, 0.233, 0.021, 1, 0.322, 0.053, 0.411, 0.625, 0.5, 0.625, 1, 0.567, 0.625, 0.633, 0, 0.7, 0, 1, 1.144, 0, 1.589, 0, 2.033, 0, 1, 2.089, 0, 2.144, 0.229, 2.2, 0.229, 1, 2.278, 0.229, 2.356, 0, 2.433, 0, 1, 2.544, 0, 2.656, 0, 2.767, 0, 1, 2.822, 0, 2.878, 0.229, 2.933, 0.229, 1, 3.011, 0.229, 3.089, 0, 3.167, 0, 1, 3.456, 0, 3.744, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0.479, 4.333, 0.479, 0, 5.367, 0.479], "Target": "Parameter"}, {"Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.078, 0, 0.156, 0, 0.233, 0, 1, 0.322, 0, 0.411, 0.625, 0.5, 0.625, 1, 0.567, 0.625, 0.633, 0, 0.7, 0, 1, 1.144, 0, 1.589, 0.005, 2.033, 0.021, 1, 2.089, 0.023, 2.144, 0.188, 2.2, 0.188, 1, 2.278, 0.188, 2.356, 0, 2.433, 0, 1, 2.544, 0, 2.656, 0.001, 2.767, 0.021, 1, 2.822, 0.031, 2.878, 0.229, 2.933, 0.229, 1, 3.011, 0.229, 3.089, 0, 3.167, 0, 1, 3.456, 0, 3.744, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0.479, 4.333, 0.479, 0, 5.367, 0.479], "Target": "Parameter"}, {"Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 1.678, 0, 2.856, 0, 4.033, 0, 1, 4.133, 0, 4.233, -0.479, 4.333, -0.479, 0, 5.367, -0.479], "Target": "Parameter"}, {"Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 1.589, 0, 2.811, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0.688, 4.333, 0.688, 0, 5.367, 0.688], "Target": "Parameter"}, {"Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 1.678, 0, 2.856, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 1.589, 0, 2.811, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 1.589, 0, 2.811, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.122, 0, 0.244, 0, 0.367, 0, 1, 1.589, 0, 2.811, 0, 4.033, 0, 1, 4.133, 0, 4.233, 0, 4.333, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamMouthForm", "Segments": [0, 1, 1, 1.367, 1, 2.733, 1, 4.1, 1, 0, 5.367, 1], "Target": "Parameter"}, {"Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 1.367, 0, 2.733, 0, 4.1, 0, 1, 4.144, 0, 4.189, 1, 4.233, 1, 0, 5.367, 1], "Target": "Parameter"}, {"Id": "ParamBodyAngleX", "Segments": [0, -10, 1, 0.078, -10, 0.156, 0, 0.233, 0, 1, 0.311, 0, 0.389, -1.353, 0.467, -1.353, 1, 0.6, -1.353, 0.733, 10, 0.867, 10, 1, 0.956, 10, 1.044, 9.799, 1.133, 9.799, 1, 1.233, 9.799, 1.333, 10, 1.433, 10, 1, 1.667, 10, 1.9, -2.975, 2.133, -2.975, 1, 2.211, -2.975, 2.289, -2.975, 2.367, -2.975, 1, 2.522, -2.975, 2.678, 9.025, 2.833, 9.025, 1, 2.978, 9.025, 3.122, 9.025, 3.267, 9.025, 1, 3.489, 9.025, 3.711, 2.025, 3.933, 2.025, 0, 5.367, 2.025], "Target": "Parameter"}, {"Id": "ParamBodyAngleY", "Segments": [0, 10, 1, 0.122, 10, 0.244, 9.141, 0.367, 5.421, 1, 0.533, 0.348, 0.7, -3.737, 0.867, -3.737, 1, 0.978, -3.737, 1.089, 2.181, 1.2, 2.181, 1, 1.3, 2.181, 1.4, 1.948, 1.5, 0.298, 1, 1.6, -1.351, 1.7, -3.396, 1.8, -3.396, 1, 1.944, -3.396, 2.089, 4.673, 2.233, 4.673, 1, 2.378, 4.673, 2.522, -3.858, 2.667, -3.858, 1, 2.767, -3.858, 2.867, 4.794, 2.967, 4.794, 1, 3.089, 4.794, 3.211, -3.573, 3.333, -3.573, 1, 3.533, -3.573, 3.733, 5.27, 3.933, 5.27, 1, 4.044, 5.27, 4.156, -5.393, 4.267, -5.393, 1, 4.356, -5.393, 4.444, 0.095, 4.533, 0.095, 0, 5.367, 0.095], "Target": "Parameter"}, {"Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.122, 0, 0.244, 2, 0.367, 2, 1, 0.533, 2, 0.7, -10, 0.867, -10, 1, 1.078, -10, 1.289, -10.013, 1.5, -9.975, 1, 1.767, -9.927, 2.033, 2.026, 2.3, 2.026, 1, 2.522, 2.026, 2.744, -9.975, 2.967, -9.975, 1, 3.322, -9.975, 3.678, -6.592, 4.033, 1.8, 1, 4.178, 5.209, 4.322, 8, 4.467, 8, 0, 5.367, 8], "Target": "Parameter"}, {"Id": "ParamBreath", "Segments": [0, 0, 1, 0.122, 0, 0.244, 1, 0.367, 1, 1, 0.511, 1, 0.656, 0, 0.8, 0, 1, 1.022, 0, 1.244, 1, 1.467, 1, 1, 1.689, 1, 1.911, 0, 2.133, 0, 1, 2.344, 0, 2.556, 1, 2.767, 1, 1, 2.967, 1, 3.167, 0, 3.367, 0, 1, 3.578, 0, 3.789, 1, 4, 1, 1, 4.233, 1, 4.467, 0, 4.7, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "ParamShoulder", "Segments": [0, 0, 1, 0.144, 0, 0.289, 1, 0.433, 1, 1, 0.6, 1, 0.767, 0.049, 0.933, -0.1, 1, 1.167, -0.309, 1.4, -0.3, 1.633, -0.3, 1, 1.844, -0.3, 2.056, 0.9, 2.267, 0.9, 1, 2.411, 0.9, 2.556, -0.22, 2.7, -0.22, 1, 2.844, -0.22, 2.989, 0.6, 3.133, 0.6, 1, 3.289, 0.6, 3.444, -0.4, 3.6, -0.4, 1, 3.7, -0.4, 3.8, 0.5, 3.9, 0.5, 1, 4.044, 0.5, 4.189, 0.238, 4.333, 0, 1, 4.444, -0.183, 4.556, -0.2, 4.667, -0.2, 0, 5.367, -0.2], "Target": "Parameter"}, {"Id": "ParamLeg", "Segments": [0, 1, 1, 1.344, 1, 2.689, 1, 4.033, 1, 1, 4.144, 1, 4.256, 0, 4.367, 0, 1, 4.578, 0, 4.789, 0.052, 5, 0.052, 0, 5.367, 0.052], "Target": "Parameter"}, {"Id": "ParamArmLB", "Segments": [0, 0, 1, 0.1, 0, 0.2, 1.3, 0.3, 1.3, 1, 0.544, 1.3, 0.789, 0, 1.033, 0, 1, 1.222, 0, 1.411, 0, 1.6, 0, 1, 1.811, 0, 2.022, -10, 2.233, -10, 1, 2.811, -10, 3.389, -10, 3.967, -10, 1, 4.133, -10, 4.3, -6.458, 4.467, -6.458, 0, 5.367, -6.458], "Target": "Parameter"}, {"Id": "ParamArmRB", "Segments": [0, 0, 1, 0.1, 0, 0.2, 1.9, 0.3, 1.9, 1, 0.544, 1.9, 0.789, 0, 1.033, 0, 1, 1.222, 0, 1.411, 0, 1.6, 0, 1, 1.811, 0, 2.022, -10, 2.233, -10, 1, 2.7, -10, 3.167, -10, 3.633, -10, 1, 3.744, -10, 3.856, -10, 3.967, -10, 1, 4.133, -10, 4.3, -5.833, 4.467, -5.833, 0, 5.367, -5.833], "Target": "Parameter"}, {"Id": "ParamHandLB", "Segments": [0, 0, 1, 0.156, 0, 0.311, 0, 0.467, 0, 1, 0.656, 0, 0.844, 6.25, 1.033, 6.25, 1, 1.222, 6.25, 1.411, 6.365, 1.6, 5.833, 1, 1.811, 5.239, 2.022, -4.07, 2.233, -4.583, 1, 2.367, -4.907, 2.5, -4.792, 2.633, -4.792, 1, 2.778, -4.792, 2.922, -3.75, 3.067, -3.75, 1, 3.256, -3.75, 3.444, -6.25, 3.633, -6.25, 1, 3.756, -6.25, 3.878, -6.419, 4, -5.833, 1, 4.156, -5.088, 4.311, 9.4, 4.467, 9.4, 1, 4.533, 9.4, 4.6, 7.1, 4.667, 7.1, 0, 5.367, 7.1], "Target": "Parameter"}, {"Id": "ParamHandRB", "Segments": [0, 0, 1, 0.156, 0, 0.311, 0, 0.467, 0, 1, 0.656, 0, 0.844, 8.064, 1.033, 8.333, 1, 1.222, 8.603, 1.411, 8.542, 1.6, 8.542, 1, 1.811, 8.542, 2.022, -5.8, 2.233, -7.292, 1, 2.367, -8.234, 2.5, -7.917, 2.633, -7.917, 1, 2.778, -7.917, 2.922, -7.917, 3.067, -7.917, 1, 3.256, -7.917, 3.444, -8.084, 3.633, -8.333, 1, 3.756, -8.494, 3.878, -8.542, 4, -8.542, 1, 4.156, -8.542, 4.311, 10, 4.467, 10, 1, 4.533, 10, 4.6, 6.8, 4.667, 6.8, 0, 5.367, 6.8], "Target": "Parameter"}, {"Id": "ParamHandL", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0.958, 0.433, 0.958, 1, 0.611, 0.958, 0.789, -1, 0.967, -1, 1, 1.222, -1, 1.478, 0.4, 1.733, 0.4, 1, 1.922, 0.4, 2.111, -1, 2.3, -1, 1, 2.544, -1, 2.789, 1, 3.033, 1, 1, 3.344, 1, 3.656, -1, 3.967, -1, 1, 4.222, -1, 4.478, 0.4, 4.733, 0.4, 0, 5.367, 0.4], "Target": "Parameter"}, {"Id": "ParamHandR", "Segments": [0, 0, 1, 0.144, 0, 0.289, 1, 0.433, 1, 1, 0.611, 1, 0.789, -1, 0.967, -1, 1, 1.222, -1, 1.478, 0.4, 1.733, 0.4, 1, 1.922, 0.4, 2.111, -1, 2.3, -1, 1, 2.544, -1, 2.789, 1, 3.033, 1, 1, 3.344, 1, 3.656, -1, 3.967, -1, 1, 4.222, -1, 4.478, 0.3, 4.733, 0.3, 0, 5.367, 0.3], "Target": "Parameter"}, {"Id": "ParamHairAhoge", "Segments": [0, 0, 1, 0.022, 0, 0.044, 0, 0.067, 0, 1, 0.144, 0, 0.222, -10, 0.3, -10, 1, 0.456, -10, 0.611, 8.967, 0.767, 8.967, 1, 0.889, 8.967, 1.011, -5.867, 1.133, -5.867, 1, 1.244, -5.867, 1.356, -3.178, 1.467, -0.111, 1, 1.522, 1.423, 1.578, 1.562, 1.633, 1.562, 1, 1.733, 1.562, 1.833, -4.563, 1.933, -4.563, 1, 2.078, -4.563, 2.222, 10, 2.367, 10, 1, 2.411, 10, 2.456, 10.159, 2.5, 9.828, 1, 2.6, 9.082, 2.7, -8.352, 2.8, -8.352, 1, 2.867, -8.352, 2.933, -8.632, 3, -7.761, 1, 3.111, -6.31, 3.222, 6.63, 3.333, 6.63, 1, 3.389, 6.63, 3.444, -4.883, 3.5, -4.883, 1, 3.544, -4.883, 3.589, -5.066, 3.633, -4.785, 1, 3.811, -3.661, 3.989, 9.803, 4.167, 9.803, 1, 4.256, 9.803, 4.344, -9.656, 4.433, -9.656, 1, 4.567, -9.656, 4.7, 5.375, 4.833, 5.375, 1, 4.967, 5.375, 5.1, 0, 5.233, 0, 0, 5.367, 0], "Target": "Parameter"}, {"Id": "PartArmA", "Segments": [0, 0, 0, 5.37, 0], "Target": "PartOpacity"}, {"Id": "PartArmB", "Segments": [0, 1, 0, 5.37, 1], "Target": "PartOpacity"}], "Meta": {"AreBeziersRestricted": false, "CurveCount": 35, "Duration": 5.37, "Fps": 30.0, "Loop": true, "Repaired": true, "TotalPointCount": 751, "TotalSegmentCount": 262, "TotalUserDataSize": 0, "UserDataCount": 0}, "Version": 3}