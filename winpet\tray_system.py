#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统托盘系统
实现桌面伴侣的系统托盘功能，包含托盘图标、托盘菜单和双击显示/隐藏功能
"""

import os
from PySide6.QtWidgets import QSystemTrayIcon, QMenu, QApplication
from PySide6.QtCore import Qt, QPoint
from PySide6.QtGui import QIcon, QAction, QPixmap, QPainter, QBrush, QColor


class SystemTray(QSystemTrayIcon):
    """系统托盘类"""
    
    def __init__(self, parent):
        """
        初始化系统托盘
        :param parent: 父窗口（PetCompanion实例）
        """
        super(SystemTray, self).__init__(parent)
        self.parent = parent
        
        # 设置托盘图标
        self._create_tray_icon()
        
        # 设置托盘菜单
        self._setup_tray_menu()
        
        # 连接信号
        self.activated.connect(self._on_tray_activated)
        
        # 显示托盘
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.show()
        else:
            print("系统托盘不可用")
            
    def _create_tray_icon(self):
        """创建托盘图标"""
        # 创建一个简单的圆形图标
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制外圆（边框）
        painter.setBrush(QBrush(QColor(100, 150, 200, 255)))
        painter.setPen(QColor(255, 255, 255, 200))
        painter.drawEllipse(2, 2, 28, 28)
        
        # 绘制内圆（中心点）
        painter.setBrush(QBrush(QColor(255, 255, 255, 200)))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(12, 12, 8, 8)
        
        painter.end()
        
        # 设置图标
        icon = QIcon(pixmap)
        self.setIcon(icon)
        
        # 设置工具提示
        self.setToolTip("桌面伴侣 - 双击显示/隐藏")
        
    def _setup_tray_menu(self):
        """设置托盘菜单"""
        self.tray_menu = QMenu()
        
        # 设置菜单样式
        self.tray_menu.setStyleSheet("""
            QMenu {
                background-color: rgba(50, 50, 50, 240);
                border: 1px solid rgba(255, 255, 255, 100);
                border-radius: 6px;
                padding: 3px;
                color: white;
                font-size: 12px;
            }
            QMenu::item {
                background-color: transparent;
                padding: 6px 16px;
                border-radius: 3px;
                margin: 1px;
            }
            QMenu::item:selected {
                background-color: rgba(100, 150, 200, 150);
            }
            QMenu::item:pressed {
                background-color: rgba(100, 150, 200, 200);
            }
            QMenu::separator {
                height: 1px;
                background-color: rgba(255, 255, 255, 50);
                margin: 3px 8px;
            }
        """)
        
        # 显示/隐藏伴侣
        self.show_hide_action = QAction("显示伴侣", self.tray_menu)
        self.show_hide_action.triggered.connect(self._toggle_companion_visibility)
        self.tray_menu.addAction(self.show_hide_action)
        
        # 分隔线
        self.tray_menu.addSeparator()
        
        # 功能设置子菜单
        self.settings_menu = QMenu("功能设置", self.tray_menu)
        self.settings_menu.setStyleSheet(self.tray_menu.styleSheet())
        
        # 置顶显示
        self.always_on_top_action = QAction("置顶显示", self.settings_menu)
        self.always_on_top_action.setCheckable(True)
        self.always_on_top_action.setChecked(True)
        self.always_on_top_action.triggered.connect(self._toggle_always_on_top)
        self.settings_menu.addAction(self.always_on_top_action)
        
        # 屏幕监控
        self.screen_monitor_action = QAction("屏幕监控", self.settings_menu)
        self.screen_monitor_action.setCheckable(True)
        self.screen_monitor_action.setChecked(True)
        self.screen_monitor_action.triggered.connect(self._toggle_screen_monitor)
        self.settings_menu.addAction(self.screen_monitor_action)
        
        # 开机启动
        self.auto_start_action = QAction("开机启动", self.settings_menu)
        self.auto_start_action.setCheckable(True)
        self.auto_start_action.setChecked(False)
        self.auto_start_action.triggered.connect(self._toggle_auto_start)
        self.settings_menu.addAction(self.auto_start_action)
        
        self.tray_menu.addMenu(self.settings_menu)
        
        # 分隔线
        self.tray_menu.addSeparator()
        
        # 关于
        self.about_action = QAction("关于", self.tray_menu)
        self.about_action.triggered.connect(self._show_about)
        self.tray_menu.addAction(self.about_action)
        
        # 分隔线
        self.tray_menu.addSeparator()
        
        # 退出程序
        self.quit_action = QAction("退出程序", self.tray_menu)
        self.quit_action.triggered.connect(self._quit_application)
        self.tray_menu.addAction(self.quit_action)
        
        # 设置托盘菜单
        self.setContextMenu(self.tray_menu)
        
    def _on_tray_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            # 双击切换显示/隐藏
            self._toggle_companion_visibility()
        elif reason == QSystemTrayIcon.Context:
            # 右键显示菜单（自动处理）
            self._update_menu_items()
            
    def _update_menu_items(self):
        """更新菜单项状态"""
        # 更新显示/隐藏文本
        if self.parent.isVisible():
            self.show_hide_action.setText("隐藏伴侣")
        else:
            self.show_hide_action.setText("显示伴侣")
            
    def _toggle_companion_visibility(self):
        """切换伴侣显示/隐藏状态"""
        self.parent.toggle_visibility()
        
    def _toggle_always_on_top(self):
        """切换置顶显示"""
        is_on_top = self.always_on_top_action.isChecked()
        
        # 获取当前窗口标志
        flags = self.parent.windowFlags()
        
        if is_on_top:
            flags |= Qt.WindowStaysOnTopHint
        else:
            flags &= ~Qt.WindowStaysOnTopHint
            
        # 应用新的窗口标志
        self.parent.setWindowFlags(flags)
        if self.parent.isVisible():
            self.parent.show()  # 重新显示窗口以应用标志
            
    def _toggle_screen_monitor(self):
        """切换屏幕监控功能"""
        is_enabled = self.screen_monitor_action.isChecked()
        # TODO: 实现屏幕监控功能
        print(f"屏幕监控功能: {'开启' if is_enabled else '关闭'}")
        
    def _toggle_auto_start(self):
        """切换开机启动"""
        is_enabled = self.auto_start_action.isChecked()
        # TODO: 实现开机启动功能
        print(f"开机启动: {'开启' if is_enabled else '关闭'}")
        
    def _show_about(self):
        """显示关于信息"""
        from PySide6.QtWidgets import QMessageBox
        
        about_text = """
        <h3>桌面伴侣 v1.0.0</h3>
        <p>一个基于 PySide6 开发的桌面伴侣应用</p>
        <p><b>主要功能：</b></p>
        <ul>
        <li>🎭 透明背景显示</li>
        <li>🖱️ 鼠标拖拽移动</li>
        <li>📋 右键菜单控制</li>
        <li>🔧 系统托盘管理</li>
        <li>📺 屏幕监控支持</li>
        </ul>
        <p><i>让您的桌面更加生动有趣！</i></p>
        """
        
        msg_box = QMessageBox()
        msg_box.setWindowTitle("关于桌面伴侣")
        msg_box.setText(about_text)
        msg_box.setWindowFlags(Qt.Dialog | Qt.WindowStaysOnTopHint)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: rgba(50, 50, 50, 240);
                color: white;
                border-radius: 8px;
            }
            QMessageBox QPushButton {
                background-color: rgba(100, 150, 200, 150);
                border: 1px solid rgba(255, 255, 255, 100);
                border-radius: 4px;
                padding: 6px 16px;
                color: white;
                font-weight: bold;
            }
            QMessageBox QPushButton:hover {
                background-color: rgba(100, 150, 200, 200);
            }
            QMessageBox QPushButton:pressed {
                background-color: rgba(80, 120, 180, 200);
            }
        """)
        msg_box.exec()
        
    def _quit_application(self):
        """退出应用程序"""
        self.parent.quit_application()
        
    def show_message(self, title, message, icon=QSystemTrayIcon.Information, timeout=3000):
        """显示托盘消息"""
        if self.isVisible():
            self.showMessage(title, message, icon, timeout)
