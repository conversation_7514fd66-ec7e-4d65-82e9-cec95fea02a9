{"Version": 3, "Meta": {"Duration": 4.233, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 97, "TotalSegmentCount": 392, "TotalPointCount": 1111, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.067, 0, 1, 0.211, 0, 0.356, 0, 0.5, 0, 1, 0.611, 0, 0.722, 30, 0.833, 30, 1, 0.956, 30, 1.078, -30, 1.2, -30, 1, 1.689, -30, 2.178, -30, 2.667, -30, 1, 2.778, -30, 2.889, 0, 3, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.333, 1, 1, 0.389, 1, 0.444, 0, 0.5, 0, 1, 0.556, 0, 0.611, 1, 0.667, 1, 1, 0.722, 1, 0.778, 0, 0.833, 0, 1, 0.944, 0, 1.056, 2, 1.167, 2, 1, 1.444, 2, 1.722, 1.768, 2, 1, 1, 2.056, 0.846, 2.111, 0, 2.167, 0, 1, 2.222, 0, 2.278, 1, 2.333, 1, 1, 2.389, 1, 2.444, 0, 2.5, 0, 1, 2.611, 0, 2.722, 0.7, 2.833, 0.7, 1, 3, 0.7, 3.167, 0.687, 3.333, 0.6, 1, 3.389, 0.571, 3.444, 0, 3.5, 0, 1, 3.556, 0, 3.611, 1, 3.667, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.333, 1, 1, 0.389, 1, 0.444, 0, 0.5, 0, 1, 0.556, 0, 0.611, 1, 0.667, 1, 1, 0.722, 1, 0.778, 0, 0.833, 0, 1, 0.944, 0, 1.056, 2, 1.167, 2, 1, 1.444, 2, 1.722, 1.768, 2, 1, 1, 2.056, 0.846, 2.111, 0, 2.167, 0, 1, 2.222, 0, 2.278, 1, 2.333, 1, 1, 2.389, 1, 2.444, 0, 2.5, 0, 1, 2.611, 0, 2.722, 0.7, 2.833, 0.7, 1, 3, 0.7, 3.167, 0.687, 3.333, 0.6, 1, 3.389, 0.571, 3.444, 0, 3.5, 0, 1, 3.556, 0, 3.611, 1, 3.667, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 0, 4.233, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.1, 1, 0.1, 1, 1.056, 0.1, 1.111, -1, 1.167, -1, 1, 1.611, -1, 2.056, -1, 2.5, -1, 1, 2.556, -1, 2.611, -1, 2.667, -1, 1, 2.722, -1, 2.778, 1, 2.833, 1, 1, 3, 1, 3.167, 1, 3.333, 1, 1, 3.444, 1, 3.556, 0, 3.667, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.056, 0, 1.111, 0.481, 1.167, 0.5, 1, 1.611, 0.649, 2.056, 0.7, 2.5, 0.7, 1, 2.556, 0.7, 2.611, 0, 2.667, 0, 1, 2.722, 0, 2.778, 0, 2.833, 0, 1, 3, 0, 3.167, 0, 3.333, 0, 1, 3.444, 0, 3.556, 0, 3.667, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp1", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp2", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp11", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp3", "Segments": [0, 0, 0, 0.067, 0, 1, 0.378, 0, 0.689, 0, 1, 0, 1, 1.167, 0, 1.333, -1, 1.5, -1, 1, 2.167, -1, 2.833, -1, 3.5, -1, 1, 3.611, -1, 3.722, 0, 3.833, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp4", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp5", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp10", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp6", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp7", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp8", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp9", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "exp12", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 0.067, 0, 1, 1.456, 0, 2.844, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0.4, 0, 0.067, 0.4, 1, 0.178, 0.4, 0.289, -0.2, 0.4, -0.2, 1, 0.456, -0.2, 0.511, 0.4, 0.567, 0.4, 1, 0.733, 0.4, 0.9, -0.2, 1.067, -0.2, 1, 1.244, -0.2, 1.422, 0.4, 1.6, 0.4, 1, 1.767, 0.4, 1.933, -0.2, 2.1, -0.2, 1, 2.256, -0.2, 2.411, 0.4, 2.567, 0.4, 1, 2.733, 0.4, 2.9, -0.2, 3.067, -0.2, 1, 3.322, -0.2, 3.578, 0, 3.833, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 0.067, 0, 1, 0.156, 0, 0.244, 1, 0.333, 1, 1, 0.444, 1, 0.556, -1, 0.667, -1, 1, 0.733, -1, 0.8, 0, 0.867, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 0.067, 0, 1, 0.322, 0, 0.578, 0, 0.833, 0, 1, 0.944, 0, 1.056, -4, 1.167, -4, 1, 1.944, -4, 2.722, -4, 3.5, -4, 1, 3.611, -4, 3.722, 0, 3.833, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 0.067, 0, 1, 0.211, 0, 0.356, 0, 0.5, 0, 1, 0.611, 0, 0.722, 1, 0.833, 1, 1, 0.944, 1, 1.056, -0.6, 1.167, -0.6, 1, 1.278, -0.6, 1.389, 0.3, 1.5, 0.3, 1, 1.611, 0.3, 1.722, 0, 1.833, 0, 1, 2.056, 0, 2.278, 0, 2.5, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "xiongwlY", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -2.106, 1, 0.1, -2.106, 0.2, 0.486, 0.3, 0.486, 1, 0.378, 0.486, 0.456, -7.832, 0.533, -7.832, 1, 0.644, -7.832, 0.756, 2.17, 0.867, 2.17, 1, 1, 2.17, 1.133, -5.739, 1.267, -5.739, 1, 1.433, -5.739, 1.6, 0.805, 1.767, 0.805, 1, 1.933, 0.805, 2.1, -5.043, 2.267, -5.043, 1, 2.422, -5.043, 2.578, 0.982, 2.733, 0.982, 1, 2.878, 0.982, 3.022, -4.24, 3.167, -4.24, 1, 3.3, -4.24, 3.433, -1.493, 3.567, -1.493, 1, 3.711, -1.493, 3.856, -2.21, 4, -2.21, 1, 4.078, -2.21, 4.156, -2.122, 4.233, -2.1]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, -0.007, 1, 0.022, -0.007, 0.044, 0, 0.067, 0, 1, 0.122, 0, 0.178, -0.945, 0.233, -0.945, 1, 0.322, -0.945, 0.411, 3.782, 0.5, 3.782, 1, 0.589, 3.782, 0.678, -4.633, 0.767, -4.633, 1, 0.878, -4.633, 0.989, 4.334, 1.1, 4.334, 1, 1.222, 4.334, 1.344, -3.533, 1.467, -3.533, 1, 1.611, -3.533, 1.756, 2.43, 1.9, 2.43, 1, 2.078, 2.43, 2.256, -1.773, 2.433, -1.773, 1, 2.6, -1.773, 2.767, 2.001, 2.933, 2.001, 1, 3.067, 2.001, 3.2, -1.933, 3.333, -1.933, 1, 3.467, -1.933, 3.6, 1.237, 3.733, 1.237, 1, 3.856, 1.237, 3.978, -0.578, 4.1, -0.578, 1, 4.144, -0.578, 4.189, -0.363, 4.233, -0.277]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -0.018, 1, 0.111, -0.018, 0.222, -0.793, 0.333, -0.793, 1, 0.433, -0.793, 0.533, 3.928, 0.633, 3.928, 1, 0.744, 3.928, 0.856, -5.748, 0.967, -5.748, 1, 1.078, -5.748, 1.189, 6.549, 1.3, 6.549, 1, 1.433, 6.549, 1.567, -6.399, 1.7, -6.399, 1, 1.844, -6.399, 1.989, 5.298, 2.133, 5.298, 1, 2.3, 5.298, 2.467, -4.526, 2.633, -4.526, 1, 2.8, -4.526, 2.967, 4.572, 3.133, 4.572, 1, 3.278, 4.572, 3.422, -3.567, 3.567, -3.567, 1, 3.7, -3.567, 3.833, 1.946, 3.967, 1.946, 1, 4.056, 1.946, 4.144, -0.086, 4.233, -0.538]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, -2.106, 1, 0.1, -2.106, 0.2, 0.486, 0.3, 0.486, 1, 0.378, 0.486, 0.456, -7.832, 0.533, -7.832, 1, 0.644, -7.832, 0.756, 2.17, 0.867, 2.17, 1, 1, 2.17, 1.133, -5.739, 1.267, -5.739, 1, 1.433, -5.739, 1.6, 0.805, 1.767, 0.805, 1, 1.933, 0.805, 2.1, -5.043, 2.267, -5.043, 1, 2.422, -5.043, 2.578, 0.982, 2.733, 0.982, 1, 2.878, 0.982, 3.022, -4.24, 3.167, -4.24, 1, 3.3, -4.24, 3.433, -1.493, 3.567, -1.493, 1, 3.711, -1.493, 3.856, -2.21, 4, -2.21, 1, 4.078, -2.21, 4.156, -2.122, 4.233, -2.1]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, -0.007, 1, 0.022, -0.007, 0.044, 0, 0.067, 0, 1, 0.122, 0, 0.178, -0.945, 0.233, -0.945, 1, 0.322, -0.945, 0.411, 3.782, 0.5, 3.782, 1, 0.589, 3.782, 0.678, -4.633, 0.767, -4.633, 1, 0.878, -4.633, 0.989, 4.334, 1.1, 4.334, 1, 1.222, 4.334, 1.344, -3.533, 1.467, -3.533, 1, 1.611, -3.533, 1.756, 2.43, 1.9, 2.43, 1, 2.078, 2.43, 2.256, -1.773, 2.433, -1.773, 1, 2.6, -1.773, 2.767, 2.001, 2.933, 2.001, 1, 3.067, 2.001, 3.2, -1.933, 3.333, -1.933, 1, 3.467, -1.933, 3.6, 1.237, 3.733, 1.237, 1, 3.856, 1.237, 3.978, -0.578, 4.1, -0.578, 1, 4.144, -0.578, 4.189, -0.363, 4.233, -0.277]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, -0.018, 1, 0.111, -0.018, 0.222, -0.793, 0.333, -0.793, 1, 0.433, -0.793, 0.533, 3.928, 0.633, 3.928, 1, 0.744, 3.928, 0.856, -5.748, 0.967, -5.748, 1, 1.078, -5.748, 1.189, 6.549, 1.3, 6.549, 1, 1.433, 6.549, 1.567, -6.399, 1.7, -6.399, 1, 1.844, -6.399, 1.989, 5.298, 2.133, 5.298, 1, 2.3, 5.298, 2.467, -4.526, 2.633, -4.526, 1, 2.8, -4.526, 2.967, 4.572, 3.133, 4.572, 1, 3.278, 4.572, 3.422, -3.567, 3.567, -3.567, 1, 3.7, -3.567, 3.833, 1.946, 3.967, 1.946, 1, 4.056, 1.946, 4.144, -0.086, 4.233, -0.538]}, {"Target": "Parameter", "Id": "EyeL1", "Segments": [0, -0.32, 1, 0.067, -0.32, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.367, 0, 0.4, 30, 0.433, 30, 1, 0.456, 30, 0.478, 30, 0.5, 30, 1, 0.544, 30, 0.589, -30, 0.633, -30, 1, 0.656, -30, 0.678, -30, 0.7, -30, 1, 0.744, -30, 0.789, 27.903, 0.833, 27.903, 1, 0.878, 27.903, 0.922, -30, 0.967, -30, 1, 1.033, -30, 1.1, -30, 1.167, -30, 1, 1.233, -30, 1.3, 30, 1.367, 30, 1, 1.422, 30, 1.478, 30, 1.533, 30, 1, 1.633, 30, 1.733, -9.933, 1.833, -9.933, 1, 1.922, -9.933, 2.011, 30, 2.1, 30, 1, 2.122, 30, 2.144, 30, 2.167, 30, 1, 2.2, 30, 2.233, -30, 2.267, -30, 1, 2.3, -30, 2.333, -30, 2.367, -30, 1, 2.411, -30, 2.456, 22.92, 2.5, 22.92, 1, 2.589, 22.92, 2.678, -29.322, 2.767, -29.322, 1, 2.878, -29.322, 2.989, 19.123, 3.1, 19.123, 1, 3.178, 19.123, 3.256, 3.451, 3.333, 3.451, 1, 3.378, 3.451, 3.422, 19.286, 3.467, 19.286, 1, 3.511, 19.286, 3.556, -30, 3.6, -30, 1, 3.644, -30, 3.689, -30, 3.733, -30, 1, 3.833, -30, 3.933, 25.379, 4.033, 25.379, 1, 4.1, 25.379, 4.167, 7.235, 4.233, 2.051]}, {"Target": "Parameter", "Id": "EyeL2", "Segments": [0, -0.216, 1, 0.067, -0.216, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.378, 0, 0.422, 18.065, 0.467, 18.065, 1, 0.533, 18.065, 0.6, -26.77, 0.667, -26.77, 1, 0.722, -26.77, 0.778, 9.319, 0.833, 9.319, 1, 0.9, 9.319, 0.967, -26.47, 1.033, -26.47, 1, 1.133, -26.47, 1.233, 30, 1.333, 30, 1, 1.389, 30, 1.444, 30, 1.5, 30, 1, 1.622, 30, 1.744, -11.625, 1.867, -11.625, 1, 1.956, -11.625, 2.044, 8.538, 2.133, 8.538, 1, 2.189, 8.538, 2.244, -30, 2.3, -30, 1, 2.311, -30, 2.322, -30, 2.333, -30, 1, 2.389, -30, 2.444, 5.506, 2.5, 5.506, 1, 2.578, 5.506, 2.656, -17.275, 2.733, -17.275, 1, 2.856, -17.275, 2.978, 15.941, 3.1, 15.941, 1, 3.189, 15.941, 3.278, 1.173, 3.367, 1.173, 1, 3.4, 1.173, 3.433, 4.972, 3.467, 4.972, 1, 3.522, 4.972, 3.578, -29.404, 3.633, -29.404, 1, 3.767, -29.404, 3.9, 21.61, 4.033, 21.61, 1, 4.1, 21.61, 4.167, 6.764, 4.233, 2.522]}, {"Target": "Parameter", "Id": "EyeR1", "Segments": [0, -0.32, 1, 0.067, -0.32, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.367, 0, 0.4, 30, 0.433, 30, 1, 0.456, 30, 0.478, 30, 0.5, 30, 1, 0.544, 30, 0.589, -30, 0.633, -30, 1, 0.656, -30, 0.678, -30, 0.7, -30, 1, 0.744, -30, 0.789, 27.903, 0.833, 27.903, 1, 0.878, 27.903, 0.922, -30, 0.967, -30, 1, 1.033, -30, 1.1, -30, 1.167, -30, 1, 1.233, -30, 1.3, 30, 1.367, 30, 1, 1.422, 30, 1.478, 30, 1.533, 30, 1, 1.633, 30, 1.733, -9.933, 1.833, -9.933, 1, 1.922, -9.933, 2.011, 30, 2.1, 30, 1, 2.122, 30, 2.144, 30, 2.167, 30, 1, 2.2, 30, 2.233, -30, 2.267, -30, 1, 2.3, -30, 2.333, -30, 2.367, -30, 1, 2.411, -30, 2.456, 22.92, 2.5, 22.92, 1, 2.589, 22.92, 2.678, -29.322, 2.767, -29.322, 1, 2.878, -29.322, 2.989, 19.123, 3.1, 19.123, 1, 3.178, 19.123, 3.256, 3.451, 3.333, 3.451, 1, 3.378, 3.451, 3.422, 19.286, 3.467, 19.286, 1, 3.511, 19.286, 3.556, -30, 3.6, -30, 1, 3.644, -30, 3.689, -30, 3.733, -30, 1, 3.833, -30, 3.933, 25.379, 4.033, 25.379, 1, 4.1, 25.379, 4.167, 7.235, 4.233, 2.051]}, {"Target": "Parameter", "Id": "EyeR2", "Segments": [0, 0.216, 1, 0.067, 0.216, 0.133, 0, 0.2, 0, 1, 0.244, 0, 0.289, 0, 0.333, 0, 1, 0.378, 0, 0.422, -18.065, 0.467, -18.065, 1, 0.533, -18.065, 0.6, 26.77, 0.667, 26.77, 1, 0.722, 26.77, 0.778, -9.319, 0.833, -9.319, 1, 0.9, -9.319, 0.967, 26.47, 1.033, 26.47, 1, 1.133, 26.47, 1.233, -30, 1.333, -30, 1, 1.389, -30, 1.444, -30, 1.5, -30, 1, 1.622, -30, 1.744, 11.625, 1.867, 11.625, 1, 1.956, 11.625, 2.044, -8.538, 2.133, -8.538, 1, 2.189, -8.538, 2.244, 30, 2.3, 30, 1, 2.311, 30, 2.322, 30, 2.333, 30, 1, 2.389, 30, 2.444, -5.506, 2.5, -5.506, 1, 2.578, -5.506, 2.656, 17.275, 2.733, 17.275, 1, 2.856, 17.275, 2.978, -15.941, 3.1, -15.941, 1, 3.189, -15.941, 3.278, -1.173, 3.367, -1.173, 1, 3.4, -1.173, 3.433, -4.972, 3.467, -4.972, 1, 3.522, -4.972, 3.578, 29.404, 3.633, 29.404, 1, 3.767, 29.404, 3.9, -21.61, 4.033, -21.61, 1, 4.1, -21.61, 4.167, -6.764, 4.233, -2.522]}, {"Target": "Parameter", "Id": "waitaozuo1", "Segments": [0, -2.954, 1, 1.411, -2.954, 2.822, -2.954, 4.233, -2.954]}, {"Target": "Parameter", "Id": "waitaozuo5", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "waitaozuo6", "Segments": [0, -2.954, 1, 1.411, -2.954, 2.822, -2.954, 4.233, -2.954]}, {"Target": "Parameter", "Id": "waitaozuo7", "Segments": [0, 0, 1, 1.411, 0, 2.822, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "waitaozuo4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 2.448, 0.3, 2.448, 1, 0.378, 2.448, 0.456, -5.096, 0.533, -5.096, 1, 0.644, -5.096, 0.756, 3.658, 0.867, 3.658, 1, 1, 3.658, 1.133, -3.014, 1.267, -3.014, 1, 1.444, -3.014, 1.622, 2.508, 1.8, 2.508, 1, 1.967, 2.508, 2.133, -2.647, 2.3, -2.647, 1, 2.456, -2.647, 2.611, 2.736, 2.767, 2.736, 1, 2.911, 2.736, 3.056, -1.719, 3.2, -1.719, 1, 3.333, -1.719, 3.467, 0.37, 3.6, 0.37, 1, 3.678, 0.37, 3.756, 0, 3.833, 0, 1, 3.967, 0, 4.1, 0, 4.233, 0]}, {"Target": "Parameter", "Id": "waitaozuo2", "Segments": [0, -0.003, 1, 0.022, -0.003, 0.044, -0.001, 0.067, -0.001, 1, 0.122, -0.001, 0.178, -1.102, 0.233, -1.102, 1, 0.333, -1.102, 0.433, 4.206, 0.533, 4.206, 1, 0.611, 4.206, 0.689, -4.824, 0.767, -4.824, 1, 0.878, -4.824, 0.989, 4.196, 1.1, 4.196, 1, 1.233, 4.196, 1.367, -3.224, 1.5, -3.224, 1, 1.644, -3.224, 1.789, 2.119, 1.933, 2.119, 1, 2.122, 2.119, 2.311, -1.889, 2.5, -1.889, 1, 2.656, -1.889, 2.811, 2.087, 2.967, 2.087, 1, 3.1, 2.087, 3.233, -1.827, 3.367, -1.827, 1, 3.489, -1.827, 3.611, 1.137, 3.733, 1.137, 1, 3.867, 1.137, 4, -0.335, 4.133, -0.335, 1, 4.167, -0.335, 4.2, -0.234, 4.233, -0.184]}, {"Target": "Parameter", "Id": "waitaozuo3", "Segments": [0, -0.015, 1, 0.122, -0.015, 0.244, -0.555, 0.367, -0.555, 1, 0.456, -0.555, 0.544, 2.153, 0.633, 2.153, 1, 0.733, 2.153, 0.833, -3.267, 0.933, -3.267, 1, 1.044, -3.267, 1.156, 3.673, 1.267, 3.673, 1, 1.4, 3.673, 1.533, -3.438, 1.667, -3.438, 1, 1.8, -3.438, 1.933, 2.665, 2.067, 2.665, 1, 2.233, 2.665, 2.4, -1.928, 2.567, -1.928, 1, 2.733, -1.928, 2.9, 1.943, 3.067, 1.943, 1, 3.211, 1.943, 3.356, -1.849, 3.5, -1.849, 1, 3.633, -1.849, 3.767, 1.332, 3.9, 1.332, 1, 4.011, 1.332, 4.122, -0.347, 4.233, -0.653]}, {"Target": "Parameter", "Id": "waitaoyou1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0, 0.033, 0, 1, 0.122, 0, 0.211, 2.319, 0.3, 2.319, 1, 0.378, 2.319, 0.456, -4.829, 0.533, -4.829, 1, 0.644, -4.829, 0.756, 3.467, 0.867, 3.467, 1, 1, 3.467, 1.133, -2.856, 1.267, -2.856, 1, 1.444, -2.856, 1.622, 2.376, 1.8, 2.376, 1, 1.967, 2.376, 2.133, -2.508, 2.3, -2.508, 1, 2.456, -2.508, 2.611, 2.599, 2.767, 2.599, 1, 2.911, 2.599, 3.056, -1.637, 3.2, -1.637, 1, 3.344, -1.637, 3.489, 0.424, 3.633, 0.424, 1, 3.789, 0.424, 3.944, -0.149, 4.1, -0.149, 1, 4.144, -0.149, 4.189, -0.054, 4.233, -0.016]}, {"Target": "Parameter", "Id": "waitaoyou2", "Segments": [0, -0.003, 1, 0.022, -0.003, 0.044, -0.001, 0.067, -0.001, 1, 0.122, -0.001, 0.178, -1.044, 0.233, -1.044, 1, 0.333, -1.044, 0.433, 3.987, 0.533, 3.987, 1, 0.611, 3.987, 0.689, -4.573, 0.767, -4.573, 1, 0.878, -4.573, 0.989, 3.977, 1.1, 3.977, 1, 1.233, 3.977, 1.367, -3.056, 1.5, -3.056, 1, 1.644, -3.056, 1.789, 2.008, 1.933, 2.008, 1, 2.122, 2.008, 2.311, -1.635, 2.5, -1.635, 1, 2.656, -1.635, 2.811, 1.964, 2.967, 1.964, 1, 3.1, 1.964, 3.233, -1.737, 3.367, -1.737, 1, 3.5, -1.737, 3.633, 0.821, 3.767, 0.821, 1, 3.911, 0.821, 4.056, -0.33, 4.2, -0.33, 1, 4.211, -0.33, 4.222, -0.325, 4.233, -0.325]}, {"Target": "Parameter", "Id": "waitaoyou3", "Segments": [0, -0.014, 1, 0.011, -0.014, 0.022, -0.015, 0.033, -0.015, 1, 0.044, -0.015, 0.056, -0.015, 0.067, -0.015, 1, 0.167, -0.015, 0.267, -0.526, 0.367, -0.526, 1, 0.456, -0.526, 0.544, 2.04, 0.633, 2.04, 1, 0.733, 2.04, 0.833, -3.096, 0.933, -3.096, 1, 1.044, -3.096, 1.156, 3.482, 1.267, 3.482, 1, 1.4, 3.482, 1.533, -3.261, 1.667, -3.261, 1, 1.8, -3.261, 1.933, 2.527, 2.067, 2.527, 1, 2.233, 2.527, 2.4, -1.811, 2.567, -1.811, 1, 2.733, -1.811, 2.9, 1.792, 3.067, 1.792, 1, 3.211, 1.792, 3.356, -1.742, 3.5, -1.742, 1, 3.633, -1.742, 3.767, 1.153, 3.9, 1.153, 1, 4.011, 1.153, 4.122, -0.244, 4.233, -0.498]}]}