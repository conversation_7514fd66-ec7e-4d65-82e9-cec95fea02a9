#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面伴侣主程序
基于DyberPet架构，实现Live2D模型显示、右键菜单和屏幕监控功能
"""

import sys
import os
from sys import platform
import ctypes
from tendo import singleton

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QLocale
from PySide6.QtGui import QIcon

from pet_widget import PetCompanion


def main():
    """主程序入口"""
    # 确保只有一个实例运行
    try:
        me = singleton.SingleInstance()
    except singleton.SingleInstanceException:
        print("桌面伴侣已经在运行中...")
        sys.exit(1)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # 关闭窗口不退出程序
    
    # 设置应用程序信息
    app.setApplicationName("桌面伴侣")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("WinPet")
    
    # 获取屏幕信息
    screens = app.screens()
    
    # 创建桌面伴侣实例
    companion = PetCompanion(screens=screens)
    
    # 显示窗口
    companion.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
