# Live2D-py 示例代码分析指南

## 📋 目录
- [难度分级](#难度分级)
- [基础入门级](#基础入门级)
- [进阶功能级](#进阶功能级)
- [高级应用级](#高级应用级)
- [专业开发级](#专业开发级)
- [调用方法总结](#调用方法总结)

## 🎯 难度分级

| 难度等级 | 示例文件 | 主要功能 | 依赖复杂度 |
|---------|---------|---------|-----------|
| ⭐ 入门 | `main_pygame_simple.py` | 基础渲染 | 低 |
| ⭐⭐ 进阶 | `main_pygame.py` | 完整功能 | 中 |
| ⭐⭐ 进阶 | `main_glfw.py` | GLFW集成 | 中 |
| ⭐⭐⭐ 高级 | `main_pygame_three_model.py` | 多模型 | 中高 |
| ⭐⭐⭐ 高级 | `main_facial_bind.py` | 面部捕捉 | 高 |
| ⭐⭐⭐⭐ 专业 | `main_pyside6_moderngl_qt.py` | 高级渲染 | 很高 |

---

## ⭐ 基础入门级

### 1. `main_pygame_simple.py` - 最简单的Live2D渲染

**功能概述**: 
- 最基础的Live2D模型加载和渲染
- 支持鼠标拖拽和点击交互
- 自动表情和动作切换

**核心代码结构**:
```python
import pygame
import live2d.v3 as live2d  # 或 live2d.v2
import resources

def main():
    # 1. 初始化
    pygame.init()
    live2d.init()
    
    # 2. 创建窗口
    display = (300, 400)
    pygame.display.set_mode(display, pygame.DOUBLEBUF | pygame.OPENGL)
    
    # 3. 初始化OpenGL
    live2d.glewInit()
    
    # 4. 加载模型
    model = live2d.LAppModel()
    model.LoadModelJson("path/to/model.json")
    model.Resize(*display)
    
    # 5. 渲染循环
    while running:
        # 事件处理
        for event in pygame.event.get():
            if event.type == pygame.MOUSEMOTION:
                model.Drag(*pygame.mouse.get_pos())
            elif event.type == pygame.MOUSEBUTTONUP:
                model.SetRandomExpression()
                model.StartRandomMotion()
        
        # 渲染
        live2d.clearBuffer()
        model.Update()
        model.Draw()
        pygame.display.flip()
```

**调用方法**:
```bash
cd examples
python main_pygame_simple.py
```

**依赖要求**:
- `pygame`
- `live2d-py`

---

### 2. `main_glfw.py` - GLFW轻量级窗口

**功能概述**:
- 使用GLFW创建轻量级OpenGL窗口
- 演示动作播放和回调机制
- 适合游戏开发场景

**核心特性**:
```python
import glfw
import live2d.v3 as live2d

# 窗口初始化
def init_window(width, height, title):
    glfw.init()
    window = glfw.create_window(width, height, title, None, None)
    glfw.make_context_current(window)
    return window

# 动作播放示例
model.StartMotion("TapBody", 3, 3, 
    onStartMotionHandler=None, 
    onFinishMotionHandler=lambda: print("动作结束"))

model.StartRandomMotion("TapBody", 3,
    onStartMotionHandler=lambda group, no: print(f"开始动作: {group} {no}"),
    onFinishMotionHandler=lambda: print("随机动作结束"))
```

**调用方法**:
```bash
python main_glfw.py
```

**依赖要求**:
- `glfw`
- `live2d-py`

---

## ⭐⭐ 进阶功能级

### 3. `main_pygame.py` - 完整功能演示

**功能概述**:
- 完整的Live2D功能演示
- 模型变换(缩放、旋转、平移)
- 音频口型同步
- 键盘控制和点击检测

**高级功能**:
```python
# 模型变换控制
model.SetOffset(dx, dy)        # 位置偏移
model.SetScale(scale)          # 缩放
model.SetRotation(rotation)    # 旋转(仅v3)

# 音频口型同步
from live2d.utils.lipsync import WavHandler
wav_handler = WavHandler("audio.wav")
model.SetParameterValue(StandardParams.ParamMouthOpenY, 
                       wav_handler.GetRms(), 1)

# 键盘控制
if event.key == pygame.K_LEFT:   dx -= 0.1
if event.key == pygame.K_RIGHT:  dx += 0.1
if event.key == pygame.K_i:      scale += 0.1
if event.key == pygame.K_r:      model.ResetPose()
```

**调用方法**:
```bash
python main_pygame.py
```

**依赖要求**:
- `pygame`
- `live2d-py`
- 音频文件(可选)

---

### 4. `main_pygame_background.py` - 背景图片集成

**功能概述**:
- 演示如何添加背景图片
- 图片渲染与Live2D模型的层次管理

**核心代码**:
```python
from live2d.utils.image import Image

# 加载背景图片
background = Image("path/to/background.png")

# 渲染顺序
while running:
    live2d.clearBuffer()
    background.Draw()  # 先绘制背景
    model.Update()
    model.Draw()       # 再绘制模型
    pygame.display.flip()
```

---

### 5. `main_tkinter.py` - Tkinter桌面应用集成

**功能概述**:
- 集成到Tkinter桌面应用
- 透明窗口效果
- 鼠标跟踪功能

**核心代码**:
```python
import tkinter
from pyopengltk import OpenGLFrame
import pyautogui

class AppOgl(OpenGLFrame):
    def initgl(self):
        live2d.init()
        live2d.glewInit()
        self.model = live2d.LAppModel()
        self.model.LoadModelJson("model.json")
    
    def redraw(self):
        # 获取鼠标位置实现跟踪
        screen_x, screen_y = pyautogui.position()
        x = screen_x - self.winfo_rootx()
        y = screen_y - self.winfo_rooty()
        
        self.model.Drag(x, y)
        self.model.Draw()

# 透明窗口设置
root = tkinter.Tk()
root.attributes('-transparent', 'black')
```

---

## ⭐⭐⭐ 高级应用级

### 6. `main_pygame_three_model.py` - 多模型同时渲染

**功能概述**:
- 同时加载和渲染多个Live2D模型
- 支持v2和v3模型混合使用
- 独立的模型控制和交互

**核心实现**:
```python
import live2d.v3 as l2d_v3
import live2d.v2 as l2d_v2

# 初始化两个版本的引擎
l2d_v3.init()
l2d_v2.init()
l2d_v3.glewInit()
l2d_v2.glewInit()

# 创建多个模型实例
model_v2 = l2d_v2.LAppModel()
model_v3_1 = l2d_v3.LAppModel()
model_v3_2 = l2d_v3.LAppModel()

# 分别加载不同模型
model_v3_1.LoadModelJson("v3/llny/llny.model3.json")
model_v3_2.LoadModelJson("v3/Haru/Haru.model3.json")
model_v2.LoadModelJson("v2/kasumi2/kasumi2.model.json")

# 设置不同位置和缩放
model_v3_1.SetOffset(-0.5, 0)
model_v3_1.SetScale(0.8)
model_v3_2.SetOffset(0.5, 0)
model_v2.SetOffset(0, -0.3)
```

**性能优化要点**:
- 合理分配GPU资源
- 避免重复的纹理加载
- 优化渲染顺序

---

### 7. `main_facial_bind.py` - 实时面部捕捉

**功能概述**:
- 使用MediaPipe进行实时面部追踪
- 将面部表情映射到Live2D参数
- 多线程处理确保流畅性

**核心技术**:
```python
import threading
from mediapipe_capture.facial_params import Params
from mediapipe_capture.new_capture_task import capture_task

# 创建面部参数对象
params = Params()

# 启动面部捕捉线程
thread = threading.Thread(target=capture_task, args=(params, 0), daemon=True)
thread.start()

# 在主渲染循环中应用参数
while running:
    if params:
        # 眼部控制
        model.SetParameterValue(StandardParams.ParamEyeLOpen, params.EyeLOpen, 1)
        model.SetParameterValue(StandardParams.ParamEyeROpen, params.EyeROpen, 1)
        
        # 嘴部控制
        model.SetParameterValue(StandardParams.ParamMouthOpenY, params.MouthOpenY, 1)
        model.SetParameterValue(StandardParams.ParamMouthForm, params.MouthForm, 1)
        
        # 头部姿态
        model.SetParameterValue(StandardParams.ParamAngleX, params.AngleX, 1)
        model.SetParameterValue(StandardParams.ParamAngleY, params.AngleY, 1)
        model.SetParameterValue(StandardParams.ParamAngleZ, params.AngleZ, 1)
        
        # 眼球位置
        model.SetParameterValue(StandardParams.ParamEyeBallX, params.EyeBallX, 1)
```

**依赖要求**:
- `mediapipe`
- `opencv-python`
- 摄像头设备

---

### 8. Qt系列 - 专业GUI集成

#### `main_pyqt5_canvas_opacity.py` - 透明度动画
```python
from live2d.utils.canvas import Canvas

class Live2DCanvas(QOpenGLWidget):
    def __init__(self):
        self.canvas = Canvas()  # 透明度控制工具
        self.total_radius = 0
    
    def timerEvent(self, a0):
        self.total_radius += math.pi * 0.5 / 120
        opacity = abs(math.cos(self.total_radius))
        self.canvas.SetOutputOpacity(opacity)  # 设置透明度
    
    def paintGL(self):
        self.model.Update()
        self.canvas.Draw(self.on_draw)  # 使用Canvas渲染
```

#### `main_pyqt5_multi_win.py` - 多窗口应用
- 每个窗口独立的Live2D模型
- 窗口间的交互和通信
- 资源管理和内存优化

---

## ⭐⭐⭐⭐ 专业开发级

### 9. `main_pyside6_moderngl_qt.py` - ModernGL高级渲染

**功能概述**:
- 结合ModernGL和Qt的高级渲染管道
- 自定义着色器和渲染效果
- 专业级图形应用开发

**核心技术**:
```python
import moderngl as mgl
from PySide6.QtOpenGLWidgets import QOpenGLWidget

class GLWidget(QOpenGLWidget):
    def initializeGL(self):
        # 创建ModernGL上下文
        self.ctx = mgl.create_context()
        
        # 初始化Live2D
        live2d.init()
        live2d.glewInit()
        self.model = live2d.LAppModel()
        
        # 自定义渲染对象
        self.triangle = Triangle(self.ctx)
    
    def paintGL(self):
        fbo = self.ctx.detect_framebuffer()
        fbo.use()
        
        # 清除屏幕
        self.ctx.clear(0.2, 0.2, 0.2, 1.0)
        
        # 渲染自定义对象
        self.triangle.Render()
        
        # 解绑VAO后渲染Live2D
        GL.glBindVertexArray(0)
        self.model.Update()
        self.model.Draw()
```

---

### 10. `motion_fix.py` - 动作文件修复工具

**功能概述**:
- 修复损坏的Live2D动作文件
- 动作文件格式分析和处理
- 批量处理工具

---

## 🚀 调用方法总结

### 环境准备
```bash
# 1. 安装基础依赖
pip install live2d-py pygame

# 2. 安装可选依赖(根据需要)
pip install PyQt5 PySide6 glfw mediapipe opencv-python

# 3. 准备模型文件
# 将Live2D模型放在 Resources/ 目录下
```

### 快速测试流程
```bash
# 1. 最简单测试
python examples/main_pygame_simple.py

# 2. 完整功能测试  
python examples/main_pygame.py

# 3. 面部捕捉测试(需要摄像头)
python examples/main_facial_bind.py

# 4. GUI框架测试
python examples/main_pyside6.py
```

### 常见问题解决
1. **模型加载失败**: 检查Resources目录和模型文件路径
2. **OpenGL错误**: 更新显卡驱动，确保OpenGL 3.3+支持
3. **面部捕捉不工作**: 检查摄像头权限和MediaPipe安装
4. **性能问题**: 降低分辨率，关闭不必要功能

---

**总结**: 建议按照难度等级逐步学习，从`main_pygame_simple.py`开始，逐步掌握Live2D-py的各种功能和应用场景。
